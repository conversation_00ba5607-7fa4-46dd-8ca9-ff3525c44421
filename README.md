# LearnSong - Music Learning App

A React + TypeScript + Vite application for learning music with YouTube videos, featuring advanced loop controls and tempo adjustment.

## Features

### Loop Controls
- **Smart Loop Creation**: The app automatically detects whether you want to create a full loop (with start and end) or a partial loop (start only)
- **Full Loops**: When both start and end times are set, creates a traditional loop for repeated playback
- **Partial Loops**: When only a start time is set, creates a bookmark/marker - perfect for marking important sections without committing to an end point
- **Live Loop Adjustment**: Hold down the "Set Current" buttons to dynamically adjust loop points while the video plays
- **Keyboard Shortcuts**:
  - `P` - Simple play/pause toggle
  - `Space` - Play/pause with loop restart (when loop is active, restarts from loop start)
  - `L` - Save loop (automatically detects full vs partial based on current state)
  - `S` - Update the currently active loop
  - `Escape` - Exit loop mode

### How It Works
The app intelligently determines what type of loop to create and when to activate looping:

**Setting Loop Points:**
- **Start Only**: Setting just a start time won't activate looping - you can continue playing normally
- **Start + End**: When both start and end are set (with at least 0.1s gap), looping automatically activates
- **No Interruption**: You can set a start time and continue listening to find the perfect end point

**Saving Loops:**
- If you have both a start and end time set, it saves a **full loop**
- If you only have a start time set, it saves a **partial loop** (bookmark)

This workflow allows you to:
- Mark interesting sections while listening without being stuck in short loops
- Take your time to find the perfect end point
- Create bookmarks for later review
- Build full loops when you're ready

Partial loops are displayed with an orange background and dashed border, and show "(start only)" in their label.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config({
  plugins: {
    // Add the react-x and react-dom plugins
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    // other rules...
    // Enable its recommended typescript rules
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
})
```
