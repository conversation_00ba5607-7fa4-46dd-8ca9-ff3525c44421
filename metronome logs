Metronome-1gm9i: Scheduling stopped. Conditions: {isPlaying: false, metronomeEnabled: true, bpm: 82, audioContextExists: true, audioContextState: 'running'}
Metronome.tsx:119 Metronome-1gm9i: Scheduling stopped. Conditions: {isPlaying: false, metronomeEnabled: false, bpm: 82, audioContextExists: true, audioContextState: 'running'}
Metronome.tsx:119 Metronome-1gm9i: Scheduling stopped. Conditions: {isPlaying: false, metronomeEnabled: true, bpm: 82, audioContextExists: true, audioContextState: 'running'}
MidiEditor.tsx:43 Video's current time: 71.326723s
MidiEditor.tsx:52 Reference start time set to: 71.326723s (relative to video)
YouTubePlayer.tsx:563 YouTubePlayer: Store state is PLAYING (Player state: 2), calling playVideo()
Metronome.tsx:171 Metronome-1gm9i: Initializing scheduling interval. VideoTime: 71.319, RefStartTime: 71.327, InitialActualTransportTime: 0.000, BeatDuration: 0.732, nextTickTimeRef set to (song time): 0.000
2MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.326723
YouTubePlayer.tsx:303 YouTube player state changed to: 3
3MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.392233
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=51.630, currentVideoTime=71.392, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=0.066, nextTickSongTime=0.000, calculated scheduledPlayTime (audioCtx)=51.564
22MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.392233
YouTubePlayer.tsx:303 YouTube player state changed to: 1
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.63591584550475
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.65291604386901
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.65791591989135
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.67391595231628
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.67791590081787
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.6899159847412
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.69491586076354
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.71291598664855
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.71791586267089
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.72191604959106
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.73191604005432
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.74791583406066
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.748916
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.75691589700317
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.76691588746642
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.76791605340576
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.78291591989135
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.7909160553131
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.79391583787536
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.80891594277954
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.81591591226196
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.81891593324279
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.83291587220764
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.83491596566772
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.84891590463256
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.85691604005432
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.86691603051757
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.873916
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.88191589700317
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.89029402098083
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.898293917984
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.90629405340576
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.91529411634826
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.92329401335144
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.9332940038147
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.94029397329712
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.94829410871887
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.95629400572204
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.96529406866455
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.96729392370605
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.98329395613098
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=52.198, currentVideoTime=71.983, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=0.657, nextTickSongTime=0.732, calculated scheduledPlayTime (audioCtx)=52.274
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 52.274 (ahead of current audioCtxTime 52.198) for transport time 0.732
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=52.274, transportTime=0.732. Called at actual audioCtxTime=52.198 (delta: -0.075). Current videoTime=71.983, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 52.274, AudioCtx.currentTime: 52.198, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.9902939256134
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 71.9992939885559
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.00629395803833
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.01529402098083
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.023293917984
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.03229398092651
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.03529400190735
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.04829401335144
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.05629391035461
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.06529397329712
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.0692939217987
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.0822939332428
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.08929390272522
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.09829396566772
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.10729402861023
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.1152939256134
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.1242939885559
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.13229388555908
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.14029402098083
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.148293917984
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.15629405340576
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.16572208392334
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.17372198092652
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.18172187792969
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.19172186839295
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.19372196185303
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.20672197329712
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.21572203623963
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.22572202670288
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.23172206866455
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.24072189318848
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.24872202861023
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.2567219256134
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.26572198855591
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.27372188555908
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.28372187602234
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.2917220114441
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.29972190844727
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.30772204386902
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.3157219408722
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.33572192179871
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.3397218703003
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.3487219332428
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.35672206866455
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.030. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.37372202861023
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.38272185313416
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.056. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.40072197901917
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.40672202098084
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.080. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.41572208392334
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.43326408392333
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.107. Resetting next tick time.
2MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.44126398092651
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.44826418882751
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.45726401335143
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.131. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.47426397329711
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.47526413923644
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.48126418119811
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.155. Resetting next tick time.
2MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.49926406866454
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.50826413160705
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.182. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.51526410108947
2MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.5332639885559
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.207. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.5402641964569
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.54826409346008
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.55626399046325
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.230. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.57426411634826
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.5792639923706
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.58326417929077
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.257. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.6012640667572
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.60526401525878
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.6102641296997
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.279. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.61526400572204
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.62926418310546
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.63126403814697
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.305. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.64026410108947
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.64826399809265
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.65726406103515
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.331. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.6652641964569
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.67326409346008
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.68226415640258
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.356. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.69042604196167
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.69842617738342
VM53663:414 
            
            
           POST https://www.youtube.com/api/stats/qoe?fmt=248&afmt=251&cpn=omreQvrY5-EULTm5&el=embedded&ns=yt&fexp=v1%2C24004644%2C434717%2C127326%2C26443548%2C53408%2C34656%2C47714%2C58316%2C18644%2C14869%2C75925%2C26895%2C9252%2C3479%2C690%2C12340%2C23206%2C7703%2C7476%2C2%2C4524%2C20977%2C9452%2C111%2C16755%2C2795%2C3415%2C1091%2C8887%2C2776%2C3338%2C2660%2C2040%2C590%2C1797%2C2846%2C1467%2C1920%2C679%2C79%2C1551%2C1145%2C1553%2C8178%2C3303%2C575&cl=766900209&seq=5&docid=qTl8TLcARco&ei=kj1FaKPSM_CEmLAP-YHM6QI&event=streamingstats&plid=AAY3CobKsK1-ZMHv&cbrand=apple&cbr=Chrome&cbrver=*********&c=WEB_EMBEDDED_PLAYER&cver=1.20250603.21.00&cplayer=UNIPLAYER&cos=Macintosh&cosver=10_15_7&cplatform=DESKTOP&cmt=68.665:71.390,68.926:71.623,69.676:72.373,70.001:72.699&vps=68.665:B,68.926:PL,70.001:PL&bwm=70.001:505926:0.039&bwe=70.001:13119947&bat=70.001:0.95:1&bh=70.001:86.036&qclc=ChBvbXJlUXZyWTUtRVVMVG01EAU net::ERR_BLOCKED_BY_CLIENT
applyHandler @ VM53663:414
apply @ VM53663:896
apply @ VM53663:896
apply @ VM53663:896
applyHandler @ VM53663:414
applyHandler @ VM53663:414
ETM @ base.js:1720
PZB @ base.js:5640
(anonymous) @ base.js:5674
d8.then @ base.js:8564
Ta1 @ base.js:5674
g.J.reportStats @ base.js:11662
(anonymous) @ base.js:5678
(anonymous) @ base.js:1679Understand this error
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.70742600190735
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.381. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=52.924, currentVideoTime=72.707, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=1.381, nextTickSongTime=1.463, calculated scheduledPlayTime (audioCtx)=53.007
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.007 (ahead of current audioCtxTime 52.924) for transport time 1.463
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.007, transportTime=1.463. Called at actual audioCtxTime=52.924 (delta: -0.083). Current videoTime=72.707, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.007, AudioCtx.currentTime: 52.924, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.7154261373291
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.72342603433228
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.73342602479553
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.407. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=52.947, currentVideoTime=72.733, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=1.407, nextTickSongTime=1.463, calculated scheduledPlayTime (audioCtx)=53.004
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.004 (ahead of current audioCtxTime 52.947) for transport time 1.463
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.004, transportTime=1.463. Called at actual audioCtxTime=52.947 (delta: -0.057). Current videoTime=72.733, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.004, AudioCtx.currentTime: 52.947, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.74042599427796
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.74842612969971
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.75742595422363
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.431. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=52.976, currentVideoTime=72.757, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=1.431, nextTickSongTime=1.463, calculated scheduledPlayTime (audioCtx)=53.009
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.009 (ahead of current audioCtxTime 52.976) for transport time 1.463
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.009, transportTime=1.463. Called at actual audioCtxTime=52.976 (delta: -0.033). Current videoTime=72.757, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.009, AudioCtx.currentTime: 52.976, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.76542608964539
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.77442615258789
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.78142612207031
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.455. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=53.000, currentVideoTime=72.781, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=1.455, nextTickSongTime=1.463, calculated scheduledPlayTime (audioCtx)=53.008
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.008 (ahead of current audioCtxTime 53.000) for transport time 1.463
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.008, transportTime=1.463. Called at actual audioCtxTime=53.000 (delta: -0.009). Current videoTime=72.781, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.008, AudioCtx.currentTime: 53.000, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.79042618501282
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.79942600953675
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.80642597901917
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.480. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.81542604196167
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.82342617738342
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.8314260743866
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.505. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.84142606484986
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.84842603433228
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.85742609727478
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.531. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.86542599427796
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.87342612969971
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.88242595422363
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.556. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.89042608964539
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.89942615258789
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.90642612207031
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.580. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.91542618501282
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.923426082016
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.93142597901917
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.605. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.94042604196167
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.94842617738342
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.95723696948242
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.631. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.96423693896485
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.97323700190735
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.9832369923706
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.657. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.99123712779236
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 72.99823709727478
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.00623699427796
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.680. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.01723715068054
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.02323695422363
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.03223701716614
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.706. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.04023715258789
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.04823704959107
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.05723711253357
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.731. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.06523700953674
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.0732371449585
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.08123704196167
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.755. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.09123703242493
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.09823700190735
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.1062371373291
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.780. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.11623712779236
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.12323709727478
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.13323708773804
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.807. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.14023705722046
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.14923712016297
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.15623708964539
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.830. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.16523715258789
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.17323704959107
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.18123694659424
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.855. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.1912369370575
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.1982371449585
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.20623704196167
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.880. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.21523710490418
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.22399585504151
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.23399584550477
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.907. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.24099581498719
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.24899595040894
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.25799577493287
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.931. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.26599591035462
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.27499597329712
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.28199594277955
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.955. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.29099600572205
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.29999583024598
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.3069957997284
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 1.980. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.32499592561341
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.32999580163575
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.33499591607666
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.008. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.34099595803833
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.35799591798401
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.36299579400635
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.031. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.36699598092652
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.37399595040894
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.38199584741211
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.055. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.39999597329712
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.40399592179871
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.4079958703003
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.081. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.42499583024598
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.43099587220765
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.43599598664856
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.104. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=53.656, currentVideoTime=73.431, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=2.104, nextTickSongTime=2.195, calculated scheduledPlayTime (audioCtx)=53.746
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.746 (ahead of current audioCtxTime 53.656) for transport time 2.195
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.746, transportTime=2.195. Called at actual audioCtxTime=53.656 (delta: -0.091). Current videoTime=73.431, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.746, AudioCtx.currentTime: 53.656, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.44199579019165
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.45999591607666
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.464995792099
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.115. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=53.679, currentVideoTime=73.442, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=2.115, nextTickSongTime=2.195, calculated scheduledPlayTime (audioCtx)=53.759
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.759 (ahead of current audioCtxTime 53.679) for transport time 2.195
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.759, transportTime=2.195. Called at actual audioCtxTime=53.679 (delta: -0.080). Current videoTime=73.442, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.759, AudioCtx.currentTime: 53.679, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.46999590653992
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.47399585504151
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.49143199046325
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.147. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=53.708, currentVideoTime=73.474, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=2.147, nextTickSongTime=2.195, calculated scheduledPlayTime (audioCtx)=53.756
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.756 (ahead of current audioCtxTime 53.708) for transport time 2.195
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.756, transportTime=2.195. Called at actual audioCtxTime=53.714 (delta: -0.042). Current videoTime=73.474, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.756, AudioCtx.currentTime: 53.714, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.4964318664856
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.50143198092651
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.50943187792969
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.175. Resetting next tick time.
Metronome.tsx:248 Metronome-1gm9i: Interval: audioCtxTime=53.731, currentVideoTime=73.501, refStartTime=71.327, bpm=82.0, actualCurrentTransportTime=2.175, nextTickSongTime=2.195, calculated scheduledPlayTime (audioCtx)=53.751
Metronome.tsx:256 Metronome-1gm9i: Interval: Scheduling tick at 53.751 (ahead of current audioCtxTime 53.731) for transport time 2.195
Metronome.tsx:81 Metronome-1gm9i: playTick scheduled for audioCtxTime=53.751, transportTime=2.195. Called at actual audioCtxTime=53.731 (delta: -0.020). Current videoTime=73.501, refStart=71.327, BPM=82.
Metronome.tsx:98 Metronome-1gm9i: Tick PRE-START. Scheduled for: 53.751, AudioCtx.currentTime: 53.731, AudioCtx.state: running
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.52643183787536
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.53043178637695
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.53543190081787
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.204. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.54143194277954
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.55843190272522
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.56343201716614
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.232. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.56743196566772
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.57343200762939
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.59043196757507
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.247. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.59443191607666
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.59843186457825
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.606432
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.280. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.61643199046325
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.62343195994568
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.63143185694885
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.305. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.64143184741211
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.64843181689453
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.65643195231628
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.330. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.66543201525879
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.67343191226196
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.68343190272522
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.357. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.68943194468689
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.69843200762939
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.70643190463257
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.380. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.71543196757507
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.72343186457825
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.731432
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.405. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.742431917984
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.74860709346008
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.75660699046325
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.430. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.76660698092651
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.77360695040893
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.78360694087219
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.457. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.79060714877319
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.79860704577636
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.80660694277954
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.480. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.81560700572204
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.8236071411438
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.83160703814697
Metronome.tsx:220 Metronome-1gm9i: Transport time jumped significantly. Old: 0.000, New: 2.505. Resetting next tick time.
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.84160702861023
MidiEditor.tsx:88 MidiEditor updatePlayhead: videoTime from getCurrentVideoTime = 73.84860699809265
YouTubePlayer.tsx:568 YouTubePlayer: Store state is PAUSED (Player state: 1), calling pauseVideo()
Metronome.tsx:119 Metronome-1gm9i: Scheduling stopped. Conditions: {isPlaying: false, metronomeEnabled: true, bpm: 82, audioContextExists: true, audioContextState: 'running'}
YouTubePlayer.tsx:303 YouTube player state changed to: 2
VM53663:414 
