<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BandLab MIDI Editor Reference</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .reference-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .note {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        /* Headset button inactive state */
        .midi-editor-note-preview-button[aria-pressed="false"] {
            opacity: 0.5;
            background-color: #f5f5f5 !important;
        }

        /* Legato button active state */
        .legato-active {
            background-color: #00C37D !important;
            color: white !important;
        }

        /* MIDI Note Styles */
        .demo-midi-note {
            position: absolute;
            height: 10px;
            background-color: rgba(0, 195, 125, 0.8);
            border: 1px solid #00C37D;
            border-radius: 2px;
            cursor: pointer;
            user-select: none;
            transition: all 0.1s ease;
            min-width: 20px;
        }

        .demo-midi-note:hover {
            background-color: rgba(0, 195, 125, 0.9);
            border-color: #00A86B;
        }

        .demo-midi-note.selected {
            background-color: rgba(255, 193, 7, 0.8);
            border-color: #FFC107;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
        }

        .demo-midi-note.selected:hover {
            background-color: rgba(255, 193, 7, 0.9);
        }

        /* Piano Roll Container */
        .demo-piano-roll {
            position: relative;
            width: 800px;
            height: 400px;
            background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            margin: 20px 0;
            overflow: hidden;
        }

        /* Piano Roll Grid */
        .demo-piano-roll::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 50px 10px;
            pointer-events: none;
        }

        /* Resize handles */
        .demo-midi-note .resize-handle {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 4px;
            background: rgba(255, 255, 255, 0.8);
            cursor: ew-resize;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .demo-midi-note:hover .resize-handle,
        .demo-midi-note.selected .resize-handle {
            opacity: 1;
        }

        .demo-midi-note .resize-handle.left {
            left: 0;
        }

        .demo-midi-note .resize-handle.right {
            right: 0;
        }
    </style>
</head>
<body>
    <div class="reference-container">
        <h1>BandLab MIDI Editor Reference</h1>
        <div class="note">
            <strong>Note:</strong> This file contains the HTML structure from BandLab's MIDI editor for reference purposes.
            The actual implementation will be created as React components in the LearnSong application.
        </div>

        <!-- Demo Piano Roll for Testing -->
        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>Interactive Demo Piano Roll</h3>
            <p>Click to create notes, drag to move them, drag edges to resize. Select multiple notes and use Legato button.</p>
            <div class="demo-piano-roll" id="demo-piano-roll">
                <!-- Notes will be dynamically created here -->
            </div>
            <div style="margin-top: 10px;">
                <button onclick="clearAllNotes()" style="margin-right: 10px; padding: 5px 10px;">Clear All Notes</button>
                <button onclick="createTestNotes()" style="padding: 5px 10px;">Create Test Notes</button>
                <span style="margin-left: 20px; font-size: 14px;">Selected: <span id="selected-count">0</span> notes</span>
            </div>
        </div>

        <!-- Original BandLab MIDI Editor Structure -->
        <section class="green mix-editor-panel is-midi-roll is-midi-roll-piano has-focus" midi-editor-focus="" ng-class="{ 'has-focus': midiEditorState.hasFocus, 'mix-editor-panel-audio': state.isVisible, 'is-midi-roll': midiEditorState.isVisible, 'is-midi-roll-piano': midiEditorState.isVisible &amp;&amp; midiEditorState.isPianoRoll, 'is-simple-keys': viewMode, }" ng-if="midiEditorState.isVisible || state.isVisible" style="--accent-color: #00C37D; --accent-color-bis: #00C37Dfc"> <div class="midi-editor-resizer" progress="vertical" progress-on-start="storeRollHeight" progress-on-move="resizeRollHeight" progress-on-end="refreshRollLayout"> </div> <div class="mix-editor-panel-header"> <div class="mix-editor-audio-input-header studio-audio-midi-editor"> <button type="button" class="ds-close-button ds-close-button-size-compact ds-close-button-color-translucent core-11-ds-close-button" ng-click="midiEditorState.isVisible = state.isVisible = false" title="Close input panel" aria-label="Close input panel"> <svg width="16" height="16" viewBox="0 0 16 16" name="x" size="16" data-v-app=""><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="x"><path fill="currentColor" d="m12 13.41 6.8 6.8 1.4-1.42L13.42 12l6.8-6.8-1.42-1.4-6.8 6.78-6.8-6.8L3.8 5.2l6.78 6.8-6.8 6.8 1.42 1.4z"></path></svg></svg> </button> <div class="mix-editor-track-header-instrument" data-v-app=""><!--v-if--><svg width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" fill-rule="evenodd" d="M4 19h16v3h2v-8.06a4 4 0 0 0-4-4h-2.48l-1.87-5.28A4 4 0 0 0 9.88 2H6a4 4 0 0 0-4 4v16h2zM4 6c0-1.1.9-2 2-2h3.88a2 2 0 0 1 1.89 1.33l2.34 6.61H18a2 2 0 0 1 2 2V17H4z" clip-rule="evenodd"></path><path fill="currentColor" d="M6 23h12v-2H6z"></path></svg></div> <div class="mix-editor-audio-input-header-title">Instrument</div> <div id="midi-editor-panel-header-fold-scale-controls"><button type="button" class="midi-editor-fold-scale-button" aria-pressed="false">fold</button><!--v-if--></div> </div> <!----><div class="midi-editor-header" ng-if="midiEditorState.isVisible" mix-editor-scrollable="" listeners="{ notesTop: true }" emitters="{ notesTop: true, notesLeft: true }"> <div class="midi-editor-header-menu" data-v-app=""><div class="row-gap-4"><div class="midi-editor-header-menu-first-row"><fieldset class="segmented-control segmented-control-compact" name="MIDI Editor Mode" style="--segmented-control-selected-label-width: 40px; --segmented-control-selected-label-background-position-x: calc(4px - 4px);"><button aria-pressed="true" type="button" value="selectNote"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" fill-rule="evenodd" d="M1.39 2.04 9.47 21.8l3.39-6.96 6.54 6.6 1.3-1.3-6.52-6.59 6.8-3.25zM9.6 17.3 4.78 5.48l11.7 4.93-4.3 2.05a1 1 0 0 0-.42.43z" clip-rule="evenodd"></path></svg></button><button aria-pressed="false" type="button" title="Turn on Note Insert Mode (⌘ + Hold)" value="addNote"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" fill-rule="evenodd" d="M21.5 8.06a2 2 0 0 0 0-2.82l-2.59-2.59a2 2 0 0 0-2.82 0L3.54 15.19l-.67 6.1 6.09-.69zm-4.41 1.59-9.05 9.04-*********-2.91 9.04-9.05zm1.41-1.41-2.59-2.59 1.59-1.59 2.59 2.59z" clip-rule="evenodd"></path></svg></button><button aria-pressed="false" type="button" title="Toggle Velocity Mode (V)" value="velocity"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" d="m4.14 3 6.95 16.59h1.84L19.87 3h-2.16L12 16.61 6.3 3z"></path></svg></button></fieldset><button type="button" class="ds-icon-button core-11-ds-button core-11-ds-button-colors ds-button-color-primary ds-icon-button-size-compact ds-icon-button-shape-circle midi-editor-note-preview-button" aria-label="Toggle note preview" aria-pressed="true" title="Note Preview" id="headset-button" onclick="toggleNotePreview()"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true" class="ds-button-icon"><path fill="currentColor" fill-rule="evenodd" d="M12.06 3C7.24 3 4 6.93 4 11.8v1.37A3 3 0 0 1 5 13h3a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1H6a4 4 0 0 1-4-4v-6.2C2 6.06 5.91 1 12.06 1 18.23 1 22 6.08 22 11.8V18a4 4 0 0 1-4 4h-2a1 1 0 0 1-1-1v-7a1 1 0 0 1 1-1h3a3 3 0 0 1 1 .17V11.8C20 6.9 16.87 3 12.06 3M20 16a1 1 0 0 0-1-1h-2v5h1a2 2 0 0 0 2-2zM6 20a2 2 0 0 1-2-2v-2.02A1 1 0 0 1 5 15h2v5z" clip-rule="evenodd"></path></svg><!--v-if--></button></div><div style="padding: 0 var(--ds-space-4);"><div class="midi-editor-velocity-controls"><div class="midi-editor-velocity-header"><span>Velocity&nbsp; <span class="tabular-nums" style="color: var(--mix-editor-text-color);">100</span></span><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny midi-editor-velocity-button-random"><!--v-if--><span class="ds-button-text">Randomize</span><!--v-if--><!--v-if--><!--v-if--></button></div><div class="mix-editor-slider-container-horizontal" draggable="true" style="--percent: 78.57142857142858%;"><div class="mix-editor-slider-horizontal"><div class="mix-editor-slider-fill-horizontal"><div class="mix-editor-slider-cursor"><!--v-if--><!--v-if--></div></div></div></div></div><div class="midi-editor-header-content-buttons row-gap-8"><div><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny ds-button-full-width" id="legato-button" onclick="applyLegato()"><!--v-if--><span class="ds-button-text">Legato</span><!--v-if--><!--v-if--><!--v-if--></button><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny ds-button-full-width"><!--v-if--><span class="ds-button-text">Humanize</span><!--v-if--><!--v-if--><!--v-if--></button></div><div><button type="button" class="studio-dropdown-picker studio-dropdown-picker-tiny tabular-nums" id="dropdown-menu-trigger-3fcdb9b3-df62-cd18-d38b-0e661642b435" aria-haspopup="menu" aria-expanded="false"><span class="studio-dropdown-picker-text">1/16</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true" class="studio-dropdown-picker-caret"><path fill="currentColor" fill-rule="evenodd" d="m12 16 .7.7-.7.71-7.7-7.7 1.4-1.42 6.3 6.3 6.3-6.3 1.4 1.42-7 7z" clip-rule="evenodd"></path></svg></button><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny"><!--v-if--><span class="ds-button-text">Quantize</span><!--v-if--><!--v-if--><!--v-if--></button></div><div style="justify-content: space-between;"><span class="typography-footnote-medium">&nbsp;Transpose</span><span class="mix-editor-button-group"><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny tabular-nums" style="width: 46px;"><!--v-if--><span class="ds-button-text">+1</span><!--v-if--><!--v-if--><!--v-if--></button><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny tabular-nums" style="width: 46px;"><!--v-if--><span class="ds-button-text">-1</span><!--v-if--><!--v-if--><!--v-if--></button><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny tabular-nums" style="width: 46px;"><!--v-if--><span class="ds-button-text">+12</span><!--v-if--><!--v-if--><!--v-if--></button><button type="button" class="ds-button core-11-ds-button core-11-ds-button-colors ds-button-color-tertiary ds-button-size-tiny tabular-nums" style="width: 46px;"><!--v-if--><span class="ds-button-text">-12</span><!--v-if--><!--v-if--><!--v-if--></button></span></div></div></div><div style="padding: 0px 4px;"><div class="midi-editor-controls-header"><svg width="12" height="12" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" d="M8 18V6h2v12zm6 0V6h2v12z"></path><path fill="currentColor" fill-rule="evenodd" d="M5 2a4 4 0 0 0-4 4v12a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4V6a4 4 0 0 0-4-4zM3 6c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" clip-rule="evenodd"></path></svg><span class="midi-editor-controls-title">Smart View</span><button type="button" class="unset-button cursor-pointer" aria-label="More information" aria-haspopup="dialog" aria-expanded="false"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path fill="currentColor" d="M12 8.1a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2M11 10v8h2v-8z"></path><path fill="currentColor" fill-rule="evenodd" d="M1 12a11 11 0 1 1 22 0 11 11 0 0 1-22 0m11-9a9 9 0 1 0 0 18 9 9 0 0 0 0-18" clip-rule="evenodd"></path></svg></button></div><div class="midi-editor-smart-view-controls"><div class="ds-switch-container ds-switch-size-default"><input id="4082990a-4459-f625-46ae-368423a81e42" type="checkbox" role="switch" aria-label="Smart View" class="ds-switch"></div><div class="midi-key-view-controls"><button type="button" class="studio-dropdown-picker studio-dropdown-picker-tiny" aria-disabled="true" id="dropdown-menu-trigger-1eb76ddb-38bc-58a1-36ef-1421dfd55fa3" aria-haspopup="menu" aria-expanded="false"><span class="studio-dropdown-picker-text">C</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true" class="studio-dropdown-picker-caret"><path fill="currentColor" fill-rule="evenodd" d="m12 16 .7.7-.7.71-7.7-7.7 1.4-1.42 6.3 6.3 6.3-6.3 1.4 1.42-7 7z" clip-rule="evenodd"></path></svg></button><button type="button" class="studio-dropdown-picker studio-dropdown-picker-tiny" aria-disabled="true" id="dropdown-menu-trigger-487e0d9f-55a8-a6ad-7187-24d8a1450f0a" aria-haspopup="menu" aria-expanded="false"><span class="studio-dropdown-picker-text">Chromatic</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true" class="studio-dropdown-picker-caret"><path fill="currentColor" fill-rule="evenodd" d="m12 16 .7.7-.7.71-7.7-7.7 1.4-1.42 6.3 6.3 6.3-6.3 1.4 1.42-7 7z" clip-rule="evenodd"></path></svg></button></div></div></div></div></div> <div class="midi-editor-keys-container"> <div ng-show="midiEditorState.isPianoRoll"> <div data-v-app=""><div class="midi-editor-keys"><div data-midi-code="127" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="126" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="125" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="124" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="123" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="122" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="121" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="120" class="midi-editor-keys-white not-mapped"><span class="tabular-nums">C9</span></div><div data-midi-code="119" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="118" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="117" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="116" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="115" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="114" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="113" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="112" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="111" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="110" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="109" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="108" class="midi-editor-keys-white"><span class="tabular-nums">C8</span></div><div data-midi-code="107" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="106" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="105" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="104" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="103" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="102" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="101" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="100" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="99" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="98" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="97" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="96" class="midi-editor-keys-white"><span class="tabular-nums">C7</span></div><div data-midi-code="95" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="94" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="93" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="92" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="91" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="90" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="89" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="88" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="87" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="86" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="85" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="84" class="midi-editor-keys-white"><span class="tabular-nums">C6</span></div><div data-midi-code="83" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="82" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="81" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="80" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="79" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="78" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="77" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="76" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="75" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="74" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="73" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="72" class="midi-editor-keys-white"><span class="tabular-nums">C5</span></div><div data-midi-code="71" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="70" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="69" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="68" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="67" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="66" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="65" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="64" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="63" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="62" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="61" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="60" class="midi-editor-keys-white"><span class="tabular-nums">C4</span></div><div data-midi-code="59" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="58" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="57" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="56" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="55" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="54" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="53" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="52" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="51" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="50" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="49" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="48" class="midi-editor-keys-white"><span class="tabular-nums">C3</span></div><div data-midi-code="47" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="46" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="45" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="44" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="43" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="42" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="41" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="40" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="39" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="38" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="37" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="36" class="midi-editor-keys-white"><span class="tabular-nums">C2</span></div><div data-midi-code="35" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="34" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="33" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="32" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="31" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="30" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="29" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="28" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="27" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="26" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="25" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="24" class="midi-editor-keys-white"><span class="tabular-nums">C1</span></div><div data-midi-code="23" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="22" class="midi-editor-keys-black"><!--v-if--></div><div data-midi-code="21" class="midi-editor-keys-white"><!--v-if--></div><div data-midi-code="20" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="19" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="18" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="17" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="16" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="15" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="14" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="13" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="12" class="midi-editor-keys-white not-mapped"><span class="tabular-nums">C0</span></div><div data-midi-code="11" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="10" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="9" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="8" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="7" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="6" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="5" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="4" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="3" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="2" class="midi-editor-keys-white not-mapped"><!--v-if--></div><div data-midi-code="1" class="midi-editor-keys-black not-mapped"><!--v-if--></div><div data-midi-code="0" class="midi-editor-keys-white not-mapped"><span class="tabular-nums">C-1</span></div></div></div> <context-menu template="https://www.bandlab.com/web-app/midi/partials/_midi-editor-header-piano-roll-context-menu-490e29d26.html"></context-menu> </div> <div class="midi-editor-drums ng-hide" ng-hide="midiEditorState.isPianoRoll || getTrack().samplerKit"><span class="midi-editor-drums-label mapped" title="Cowbell" data-midi-code="56" ng-class="{ active: isActive(56), mapped: isMapped(56) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-cowbell" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-cowbell"><path fill="currentColor" fill-rule="evenodd" d="M11.56 1a3 3 0 0 0-2.9 2.27l-.44 1.74a3 3 0 0 0-2.57 2.26L3.3 16.7Q3 17.3 3 18v1a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v-1q0-.7-.3-1.3l-2.35-9.43A3 3 0 0 0 15.78 5l-.43-1.74A3 3 0 0 0 12.44 1zM8.88 7h6.56a1 1 0 0 1 .97.75l1.81 7.26L18 15H5.78l1.81-7.25a1 1 0 0 1 .97-.76zm4.84-2-.31-1.24a1 1 0 0 0-.97-.76h-.88a1 1 0 0 0-.97.76l-.3 1.23zM19 18.11l-.17-.68A1 1 0 0 0 18 17H6a1 1 0 0 0-.83.44l-.17.68V19a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Ride Bell" data-midi-code="53" ng-class="{ active: isActive(53), mapped: isMapped(53) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-cymbal-ride-bell" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-cymbal-ride-bell"><path fill="currentColor" fill-rule="evenodd" d="M10.42 5.6a34 34 0 0 1 5.97-3.82Q18.06.97 19.43.74c.84-.12 1.9-.08 *********.8.56 1.87.31 2.68-.27.86-.8 1.84-1.5 2.84A37 37 0 0 1 14 14.17V22h-2v-6.26a32 32 0 0 1-5.04 3.12q-1.67.81-3.04 1.04c-.83.12-1.9.08-2.6-.73-.69-.8-.56-1.87-.31-2.67.27-.87.8-1.84 1.5-2.85a34 34 0 0 1 4.47-5.08q.42-.45.9-.9l-1.62-1.8 1.48-1.34L9.4 6.37zM9.4 9.36l.87.96L11.74 9l-.83-.93.83-.67q1.36-.92 2.47-1.37a4 4 0 0 1 1.45-.35l-.02.13q-.07.44-.55 1.25a16 16 0 0 1-3.22 3.63 16 16 0 0 1-4.09 2.62 4 4 0 0 1-1.44.35l.01-.13q.08-.44.55-1.25a13 13 0 0 1 2.5-2.9" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Ride Cymbal" data-midi-code="51" ng-class="{ active: isActive(51), mapped: isMapped(51) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-cymbal-ride" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-cymbal-ride"><path fill="currentColor" fill-rule="evenodd" d="M7.91 7.71a35 35 0 0 0-5.4 5.94c-.7 1-1.23 1.98-1.5 2.85-.25.8-.38 1.87.31 ********* 1.77.85 2.6.73q1.37-.21 3.04-1.04A32 32 0 0 0 12 15.74V22h2v-7.83l.56-.47A37 37 0 0 0 20.85 7a12 12 0 0 0 1.5-2.85c.24-.8.37-1.87-.32-2.68-.7-.8-1.77-.85-2.6-.73-.9.14-1.94.51-3.04 1.04A36 36 0 0 0 9.43 6.4L7.74 4.53 6.26 5.86zM3.6 17.7q-.46.06-.57.01c0-.08 0-.25.1-.55a9 9 0 0 1 1.2-2.25q1.03-1.5 2.7-3.25c.********** 1.13.21q.55-.1 1.05-.35a11 11 0 0 0 2.21-1.5 11 11 0 0 0 1.82-1.94c.2-.3.4-.64.5-.98.08-.23.18-.7-.03-1.16q2-1.37 3.64-2.16a9 9 0 0 1 2.4-.84q.46-.05.57 0c0 .07 0 .24-.1.55a9 9 0 0 1-1.2 2.24 35 35 0 0 1-5.9 6.28A35 35 0 0 1 6 16.86a9 9 0 0 1-2.4.84" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Crash Cymbal" data-midi-code="49" ng-class="{ active: isActive(49), mapped: isMapped(49) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-cymbal-crash" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-cymbal-crash"><path fill="currentColor" fill-rule="evenodd" d="M7.91 7.71a35 35 0 0 0-5.4 5.94c-.7 1-1.23 1.98-1.5 2.85-.25.8-.38 1.87.31 ********* 1.77.85 2.6.73q1.37-.21 3.04-1.04A32 32 0 0 0 12 15.74V22h2v-7.83l.56-.47A37 37 0 0 0 20.85 7a12 12 0 0 0 1.5-2.85c.24-.8.37-1.87-.32-2.68-.7-.8-1.77-.85-2.6-.73-.9.14-1.94.51-3.04 1.04A36 36 0 0 0 9.43 6.4L7.74 4.53 6.26 5.86zm-4.88 10q.1.05.57-.01a9 9 0 0 0 2.4-.84c2-.96 4.55-2.65 7.12-4.85a35 35 0 0 0 5.9-6.28 9 9 0 0 0 1.2-2.24c.1-.3.1-.48.1-.56a2 2 0 0 0-.57.01 9 9 0 0 0-2.4.84 34 34 0 0 0-6.44 4.28l.83.93-1.48 1.33-.87-.96a33 33 0 0 0-5.06 5.55 9 9 0 0 0-1.2 2.25c-.1.3-.1.47-.1.55" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Open Hi-Hat" data-midi-code="46" ng-class="{ active: isActive(46), mapped: isMapped(46) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-hi-hat-open" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-hi-hat-open"><path fill="currentColor" fill-rule="evenodd" d="M11 5.02c-2.17.07-4.16.4-5.68.91-.87.3-1.65.66-2.23 1.12C2.52 7.5 2 8.15 2 9s.52 1.5 1.09 1.95q.**********-.34.16-.63.34A2.6 2.6 0 0 0 2 14c0 .85.52 1.5 1.09 ********** 1.36.83 2.23 1.12 1.52.5 3.5.84 5.68.91V21h2v-3.02c2.17-.07 4.16-.4 5.68-.91.87-.3 1.65-.66 2.23-1.12.57-.45 1.09-1.1 1.09-1.95 0-1-.55-1.7-1.25-2.17q-.3-.2-.64-.36.45-.24.8-.52C21.48 10.5 22 9.85 22 9s-.52-1.5-1.09-1.95a8 8 0 0 0-2.23-1.12c-1.52-.5-3.5-.84-5.68-.91V1h-2zm0 2V9h2V7.02c2 .08 3.76.38 5.05.8q1.13.4 *********.2.3.3l.03.06v.04l-.04.06q-.05.1-.29.3-.5.4-1.62.8c-1.5.5-3.64.82-6.05.82s-4.55-.33-6.05-.83a6 6 0 0 1-1.62-.79 1 1 0 0 1-.3-.3L4 9.02v-.04l.04-.06q.05-.1.29-.3.5-.4 1.62-.8A19 19 0 0 1 11 7.03M11 14v-1.02a24 24 0 0 1-4.02-.47l-.7.21a8 8 0 0 0-1.92.76c-.32.21-.36.36-.36.52v.02l.04.06q.*********.5.4 1.62.8c1.5.5 3.64.82 6.05.82s4.55-.33 6.05-.83q1.13-.39 1.62-.79.24-.2.3-.3l.03-.06V14c0-.14-.03-.28-.37-.5q-.55-.38-1.92-.78h-.03l-.56-.23A24 24 0 0 1 13 13v1z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Pedal Hi-Hat" data-midi-code="44" ng-class="{ active: isActive(44), mapped: isMapped(44) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-hi-hat-semi-opened" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-hi-hat-semi-opened"><path fill="currentColor" fill-rule="evenodd" d="M11 6.02c-2.17.07-4.16.4-5.68.91-.87.3-1.65.66-2.23 1.12C2.52 8.5 2 9.15 2 10s.52 1.5 1.09 1.95c.58.46 1.36.83 2.23 1.12 1.76.59 4.12.93 6.68.93s4.92-.34 6.68-.93c.87-.3 1.65-.66 2.23-1.12.57-.45 1.09-1.1 1.09-1.95s-.52-1.5-1.09-1.95a8 8 0 0 0-2.23-1.12c-1.52-.5-3.5-.84-5.68-.91V2h-2zm0 2V10h2V8.02c2 .08 3.76.38 5.05.8q1.13.4 *********.2.3.3l.03.06v.04l-.04.06q-.05.1-.29.3-.5.4-1.62.8c-1.5.5-3.64.82-6.05.82s-4.55-.33-6.05-.83a6 6 0 0 1-1.62-.79 1 1 0 0 1-.3-.3L4 10.02v-.04l.04-.06q.05-.1.29-.3.5-.4 1.62-.8A19 19 0 0 1 11 8.03" clip-rule="evenodd"></path><path fill="currentColor" d="M2 13.5h1.19q.87.64 2.13 1.05 1.12.37 2.51.6c1.22.22 2.64.36 4.17.36a23 23 0 0 0 6.68-.96 8 8 0 0 0 2.13-1.04H22c0 .85-.52 1.5-1.09 1.95-.58.46-1.36.82-2.23 1.12-1.52.5-3.5.83-5.68.9V21h-2v-3.51a21 21 0 0 1-5.68-.91 8 8 0 0 1-2.23-1.12C2.52 15 2 14.36 2 13.5"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Closed Hi-Hat" data-midi-code="42" ng-class="{ active: isActive(42), mapped: isMapped(42) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-hi-hat-closed" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-hi-hat-closed"><path fill="currentColor" fill-rule="evenodd" d="M11 7.02c-2.17.07-4.16.4-5.68.91-.87.3-1.65.66-2.23 1.12C2.52 9.5 2 10.15 2 11s.52 1.5 1.09 1.95c.58.46 1.36.83 2.23 1.12 1.76.59 4.12.93 6.68.93s4.92-.34 6.68-.93c.87-.3 1.65-.66 2.23-1.12.57-.45 1.09-1.1 1.09-1.95s-.52-1.5-1.09-1.95a8 8 0 0 0-2.23-1.12c-1.52-.5-3.5-.84-5.68-.91V2h-2zm0 2V11h2V9.02c2 .08 3.76.38 5.05.8q1.13.4 *********.2.3.3l.03.06v.04l-.04.06q-.05.1-.29.3-.5.4-1.62.8c-1.5.5-3.64.82-6.05.82s-4.55-.33-6.05-.83a6 6 0 0 1-1.62-.79 1 1 0 0 1-.3-.3L4 11.02v-.04l.04-.06q.05-.1.29-.3.5-.4 1.62-.8A19 19 0 0 1 11 9.03" clip-rule="evenodd"></path><path fill="currentColor" d="M11 21v-4h2v4z"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="High Tom" data-midi-code="50" ng-class="{ active: isActive(50), mapped: isMapped(50) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-hi-tom-3" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-hi-tom-3"><path fill="currentColor" fill-rule="evenodd" d="M12 7c-1.73 0-3.35.23-4.56.64-.6.2-1.16.46-1.59.8A2 2 0 0 0 5 10v5c0 1.41 1.12 2.42 2.31 3.02 1.25.62 2.91.98 4.69.98 1.8 0 3.46-.4 4.7-1.05a5 5 0 0 0 1.59-1.2c.4-.47.71-1.07.71-1.75v-5a2 2 0 0 0-.85-1.56 5 5 0 0 0-1.6-.8C15.36 7.24 13.74 7 12 7M8.07 9.53q-.71.25-.***********.97.47A13 13 0 0 0 12 11a13 13 0 0 0 3.93-.53q.71-.25.97-.47-.27-.21-.97-.47A13 13 0 0 0 12 9a13 13 0 0 0-3.93.53M17 12.2l-.44.16c-1.21.4-2.83.64-4.56.64a15 15 0 0 1-5-.8V15c0 .25.23.74 1.2 ********** 2.26.77 3.8.77a8.4 8.4 0 0 0 3.78-.83q.7-.37 1-.72c.2-.23.22-.38.22-.45z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="High Tom" data-midi-code="48" ng-class="{ active: isActive(48), mapped: isMapped(48) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-hi-tom-4" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-hi-tom-4"><path fill="currentColor" fill-rule="evenodd" d="M7.44 6.64C8.65 6.24 10.27 6 12 6s3.35.23 4.56.64c.6.2 1.16.46 1.59.8A2 2 0 0 1 19 9v7a2 2 0 0 1-.85 1.56q-.66.5-1.6.8c-1.2.4-2.82.64-4.55.64s-3.35-.23-4.56-.64a5 5 0 0 1-1.59-.8A2 2 0 0 1 5 16V9a2 2 0 0 1 .85-1.56c.43-.34.99-.6 1.6-.8M7.1 9q.27-.21.97-.47A13 13 0 0 1 12 8a13 13 0 0 1 3.93.53q.***********-.27.21-.97.47A13 13 0 0 1 12 10a13 13 0 0 1-3.93-.53Q7.36 9.22 7.1 9M7 15.91l.09.08q.27.22.98.48A13 13 0 0 0 12 17a13 13 0 0 0 3.93-.53A3 3 0 0 0 17 15.9v-4.7l-.44.15c-1.21.4-2.83.64-4.56.64a15 15 0 0 1-5-.8z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Mid Tom" data-midi-code="47" ng-class="{ active: isActive(47), mapped: isMapped(47) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-mid-tom-4" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-mid-tom-4"><path fill="currentColor" fill-rule="evenodd" d="M6.1 6.62C7.65 6.22 9.73 6 12 6s4.35.23 5.9.62c.77.19 1.46.43 **********.27 1.12.8 1.12 1.64v7c0 .8-.44 1.45-.97 1.92-.52.46-1.23.84-2.02 1.14-1.59.6-3.71.94-6.01.94s-4.42-.35-6-.94q-1.22-.44-2.03-1.14A2.6 2.6 0 0 1 3 16V9c0-.85.66-1.37 1.12-1.64.52-.3 1.21-.55 1.98-.74M5.3 9q.44-.23 1.3-.44C7.93 8.22 9.85 8 12 8s4.07.22 5.41.56q.86.22 1.3.44-.44.23-1.3.44c-1.34.34-3.26.56-5.41.56s-4.07-.22-5.41-.56q-.86-.22-1.3-.44M5 16c0 .**********.42q.4.39 1.4.76c1.3.5 3.18.82 5.3.82s4-.32 5.3-.82a5 5 0 0 0 1.4-.76c.29-.25.3-.4.3-.42v-4.95q-.51.2-1.1.33c-1.55.4-3.63.62-5.9.62s-4.35-.23-5.9-.62q-.59-.14-1.1-.33z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Mid Tom" data-midi-code="45" ng-class="{ active: isActive(45), mapped: isMapped(45) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-mid-tom-3" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-mid-tom-3"><path fill="currentColor" fill-rule="evenodd" d="M6.1 5.62C7.65 5.22 9.73 5 12 5s4.35.23 5.9.62c.77.19 1.46.43 **********.27 1.12.8 1.12 1.64v9c0 .8-.44 1.45-.97 1.92-.52.46-1.23.84-2.02 1.14-1.59.6-3.71.94-6.01.94s-4.42-.35-6-.94q-1.22-.44-2.03-1.14A2.6 2.6 0 0 1 3 17V8c0-.85.66-1.37 1.12-1.64.52-.3 1.21-.55 1.98-.74M5.3 8q.44-.23 1.3-.44C7.93 7.22 9.85 7 12 7s4.07.22 5.41.56q.86.22 1.3.44-.44.23-1.3.44C16.07 8.78 14.15 9 12 9s-4.07-.22-5.41-.56q-.86-.22-1.3-.44m13.77-.22v-.01zm-14.12 0v-.01zM5 17c0 .**********.42q.4.39 1.4.76c1.3.5 3.18.82 5.3.82s4-.32 5.3-.82a5 5 0 0 0 1.4-.76c.29-.25.3-.4.3-.42v-6.95q-.51.2-1.1.33c-1.55.4-3.63.62-5.9.62s-4.35-.23-5.9-.62q-.59-.14-1.1-.33z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Floor Tom" data-midi-code="43" ng-class="{ active: isActive(43), mapped: isMapped(43) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-low-tom-4" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-low-tom-4"><path fill="currentColor" fill-rule="evenodd" d="M4.64 4.92C6.57 4.34 9.17 4 12 4s5.43.34 7.36.92c.96.29 1.8.65 2.43 1.1C22.4 6.45 23 7.1 23 8v10c0 .9-.6 1.55-1.2 1.98a9 9 0 0 1-2.44 1.1c-1.93.58-4.53.92-7.36.92s-5.43-.34-7.36-.92a9 9 0 0 1-2.43-1.1C1.6 19.55 1 18.9 1 18V8c0-.9.6-1.55 1.2-1.98a9 9 0 0 1 2.44-1.1M3.01 8c.**********.35.35q.58.42 1.86.81C6.9 9.67 9.3 10 12 10s5.1-.33 6.78-.84a7 7 0 0 0 1.86-.8c.26-.2.33-.32.35-.36a1 1 0 0 0-.35-.35 7 7 0 0 0-1.86-.81C17.1 6.33 14.7 6 12 6s-5.1.33-6.78.84a7 7 0 0 0-1.86.8c-.26.2-.33.32-.35.36M3 17.99c0 .***********.36q.58.42 1.86.81C6.9 19.67 9.3 20 12 20s5.1-.33 6.78-.84a7 7 0 0 0 1.86-.8c.3-.22.35-.35.36-.37v-7.54q-.74.36-1.64.63c-1.93.58-4.53.92-7.36.92s-5.43-.34-7.36-.92A11 11 0 0 1 3 10.45z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Floor Tom" data-midi-code="41" ng-class="{ active: isActive(41), mapped: isMapped(41) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-low-tom-3" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-low-tom-3"><path fill="currentColor" fill-rule="evenodd" d="M4.64 2.92C6.57 2.34 9.17 2 12 2s5.43.34 7.36.92c.96.29 1.8.65 2.43 1.1C22.4 4.45 23 5.1 23 6v13c0 .9-.6 1.55-1.2 1.98a9 9 0 0 1-2.44 1.1c-1.93.58-4.53.92-7.36.92s-5.43-.34-7.36-.92a9 9 0 0 1-2.43-1.1C1.6 20.55 1 19.9 1 19V6c0-.9.6-1.55 1.2-1.98a9 9 0 0 1 2.44-1.1M3.01 6c.**********.35.35q.58.42 1.86.81C6.9 7.67 9.3 8 12 8s5.1-.33 6.78-.84a7 7 0 0 0 1.86-.8c.26-.2.33-.32.35-.36a1 1 0 0 0-.35-.35 7 7 0 0 0-1.86-.81C17.1 4.33 14.7 4 12 4s-5.1.33-6.78.84a7 7 0 0 0-1.86.8c-.26.2-.33.32-.35.36M3 18.99c0 .***********.36q.58.42 1.86.81C6.9 20.67 9.3 21 12 21s5.1-.33 6.78-.84a7 7 0 0 0 1.86-.8c.3-.22.35-.35.36-.37V8.45q-.74.36-1.64.63c-1.93.58-4.53.92-7.36.92s-5.43-.34-7.36-.92A11 11 0 0 1 3 8.45z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Electric Snare" data-midi-code="40" ng-class="{ active: isActive(40), mapped: isMapped(40) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-snare-electric" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-snare-electric"><path fill="currentColor" fill-rule="evenodd" d="M2.15 6.37A2.8 2.8 0 0 0 1 8.5V16c0 .92.47 1.69 1.1 2.28a8 8 0 0 0 2.46 1.48A21 21 0 0 0 12 21c2.86 0 5.49-.46 7.44-1.24a8 8 0 0 0 2.46-1.48c.63-.6 1.1-1.36 1.1-2.28V8.5c0-.9-.52-1.62-1.15-2.13a8 8 0 0 0-2.45-1.29A23 23 0 0 0 12 4c-2.84 0-5.46.4-7.4 1.08-.97.34-1.82.76-2.45 1.29M3.42 7.9c-.38.32-.42.52-.42.59s.04.27.42.6c.38.3 1 .64 1.84.93 1.68.59 4.06.97 6.74.97s5.06-.38 6.74-.97q1.27-.46 1.84-.94c.38-.32.42-.52.42-.59s-.04-.27-.42-.6c-.38-.3-1-.64-1.84-.93A21 21 0 0 0 12 6c-2.68 0-5.06.38-6.74.97Q4 7.43 3.42 7.9M11 12.98V15h2v-2.02q2.16-.07 4-.43V14h2v-1.95l.4-.13q.88-.3 1.6-.7V16c0 .19-.1.47-.47.83q-.51.49-1.53.94V16h-2v2.44q-1.77.44-4 .54V17h-2v1.98a20 20 0 0 1-4-.54V16H5v1.77q-1.03-.46-1.53-.94C3.1 16.47 3 16.19 3 16v-4.79a10 10 0 0 0 2 .84V14h2v-1.45q1.84.37 4 .43" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Clap" data-midi-code="39" ng-class="{ active: isActive(39), mapped: isMapped(39) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-clap" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-clap"><path fill="currentColor" d="M21.13 2a1.1 1.1 0 1 1-2.2 0 1.1 1.1 0 0 1 2.2 0"></path><path fill="currentColor" fill-rule="evenodd" d="M4.28 2.54a3 3 0 0 1 3.61.47 3 3 0 0 1 4.95.36l2.09 3.61a3 3 0 0 1 4.88.45l1 1.73a9 9 0 0 1-15.59 9l-3.5-6.06a3 3 0 0 1 2.05-4.45l-.59-1.01a3 3 0 0 1 1.1-4.1m.64 3.1a1 1 0 0 1 1.73-1l3.5 6.06 1.73-1-2.5-4.33a1 1 0 1 1 1.73-1l3.5 6.06 1 1.73 1.73-1-1-1.73a1 1 0 0 1 1.74-1l1 1.73a7 7 0 1 1-12.13 7l-3.5-6.06a1 1 0 0 1 1.73-1l1.5 2.6 1.74-1-1.5-2.6H6.9z" clip-rule="evenodd"></path><path fill="currentColor" d="M16.02 4.09a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2m6.01 3a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Snare" data-midi-code="38" ng-class="{ active: isActive(38), mapped: isMapped(38) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-snare" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-snare"><path fill="currentColor" fill-rule="evenodd" d="M1 9v7c0 .92.47 1.69 1.1 2.28A8 8 0 0 0 5 19.92l.22.08c1.87.63 4.23 1 6.78 1s4.91-.37 6.78-1l.22-.08.44-.16a8 8 0 0 0 2.46-1.48c.63-.6 1.1-1.36 1.1-2.28V9a3 3 0 0 0-1.15-1.63 8 8 0 0 0-2.45-1.29A23 23 0 0 0 12 5c-2.84 0-5.46.4-7.4 1.08-.97.34-1.82.76-2.45 1.29-.5.41-1 .96-1.15 1.63m2 .5c0-.07.04-.27.42-.6.38-.3 1-.64 1.84-.93C6.94 7.38 9.32 7 12 7s5.06.38 6.74.97q1.27.46 1.84.94c.***********.42.59s-.04.27-.42.6c-.38.3-1 .64-1.84.93-1.68.59-4.06.97-6.74.97s-5.06-.38-6.74-.97a6 6 0 0 1-1.84-.94C3.04 9.77 3 9.57 3 9.5m4 8.94q1.77.44 4 .54v-5q-2.16-.07-4-.43zm-2-5.39-.4-.13a10 10 0 0 1-1.6-.7V16c0 .**********.83q.51.49 1.53.94zm14 4.72q1.03-.46 1.53-.94c.38-.36.47-.64.47-.83v-3.79a10 10 0 0 1-2 .84zm-2-4.22q-1.84.37-4 .43v5q2.23-.1 4-.54z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Snare Edge" data-midi-code="37" ng-class="{ active: isActive(37), mapped: isMapped(37) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-rim" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-rim"><path fill="currentColor" d="m11.37 9.11-9.9-6.27 1.06-1.68 9.89 6.25.03-.01a1.5 1.5 0 1 1-1.08 1.71"></path><path fill="currentColor" fill-rule="evenodd" d="M18.74 7.97q-1.39-.49-3.32-.75L11.94 5H12c2.84 0 5.46.4 7.4 ********** 1.82.77 2.45 1.29A2.8 2.8 0 0 1 23 9.5V16c0 .92-.47 1.69-1.1 2.28a8 8 0 0 1-2.46 1.48A21 21 0 0 1 12 21a21 21 0 0 1-7.44-1.24 8 8 0 0 1-2.46-1.48A3.2 3.2 0 0 1 1 16V9.5c0-.9.52-1.62 1.15-2.13q.61-.5 1.45-.89l2.27 1.3-.61.19q-1.26.46-1.84.94c-.38.32-.42.52-.42.59s.04.27.42.6c.38.3 1 .64 1.84.93 1.68.59 4.06.97 6.74.97s5.06-.38 6.74-.97q1.27-.46 1.84-.94c.38-.32.42-.52.42-.59s-.04-.27-.42-.6c-.38-.3-1-.64-1.84-.93M3.47 16.83C3.1 16.47 3 16.19 3 16v-3.79a10 10 0 0 0 2 .84v4.72q-1.03-.46-1.53-.94M7 18.43q1.77.45 4 .55v-5q-2.16-.07-4-.43zm6 .55v-5q2.16-.07 4-.43v4.89q-1.77.44-4 .54m6-1.2q1.03-.46 1.53-.95c.38-.36.47-.64.47-.83v-3.79a10 10 0 0 1-2 .84z" clip-rule="evenodd"></path></svg></svg></span><span class="midi-editor-drums-label mapped" title="Kick" data-midi-code="36" ng-class="{ active: isActive(36), mapped: isMapped(36) }"><svg width="24" height="24" viewBox="0 0 24 24" size="24" name="instrument-drum-kick" data-v-app=""><svg width="24" height="24" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="instrument-drum-kick"><path fill="currentColor" fill-rule="evenodd" d="M12 1a11 11 0 1 0 0 22 11 11 0 0 0 0-22M3 12a9 9 0 1 1 12.9 8.11 4 4 0 0 0-2.9-2.98v-2.3a3 3 0 1 0-2 0v2.3a4 4 0 0 0-2.9 2.98A9 9 0 0 1 3 12m7.01 8.78a9 9 0 0 0 3.98 0 2 2 0 0 0-3.98 0M12 11a1 1 0 1 0 0 2 1 1 0 0 0 0-2" clip-rule="evenodd"></path><path fill="currentColor" d="M20.98 23.1a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2M4.12 22a1.1 1.1 0 1 1-2.2 0 1.1 1.1 0 0 1 2.2 0"></path></svg></svg></span></div> <div class="midi-editor-pads ng-hide" ng-hide="!getTrack().samplerKit"><span class="midi-editor-pad-label  mapped" data-midi-code="51" ng-class="{ active: isActive(51), mapped: isMapped(51) }">7</span><span class="midi-editor-pad-label  mapped" data-midi-code="50" ng-class="{ active: isActive(50), mapped: isMapped(50) }">6</span><span class="midi-editor-pad-label  mapped" data-midi-code="49" ng-class="{ active: isActive(49), mapped: isMapped(49) }">5</span><span class="midi-editor-pad-label  mapped" data-midi-code="48" ng-class="{ active: isActive(48), mapped: isMapped(48) }">4</span><span class="midi-editor-pad-label  mapped" data-midi-code="47" ng-class="{ active: isActive(47), mapped: isMapped(47) }">U</span><span class="midi-editor-pad-label  mapped" data-midi-code="46" ng-class="{ active: isActive(46), mapped: isMapped(46) }">Y</span><span class="midi-editor-pad-label  mapped" data-midi-code="45" ng-class="{ active: isActive(45), mapped: isMapped(45) }">T</span><span class="midi-editor-pad-label  mapped" data-midi-code="44" ng-class="{ active: isActive(44), mapped: isMapped(44) }">R</span><span class="midi-editor-pad-label  mapped" data-midi-code="43" ng-class="{ active: isActive(43), mapped: isMapped(43) }">J</span><span class="midi-editor-pad-label  mapped" data-midi-code="42" ng-class="{ active: isActive(42), mapped: isMapped(42) }">H</span><span class="midi-editor-pad-label  mapped" data-midi-code="41" ng-class="{ active: isActive(41), mapped: isMapped(41) }">G</span><span class="midi-editor-pad-label  mapped" data-midi-code="40" ng-class="{ active: isActive(40), mapped: isMapped(40) }">F</span><span class="midi-editor-pad-label  mapped" data-midi-code="39" ng-class="{ active: isActive(39), mapped: isMapped(39) }">M</span><span class="midi-editor-pad-label  mapped" data-midi-code="38" ng-class="{ active: isActive(38), mapped: isMapped(38) }">N</span><span class="midi-editor-pad-label  mapped" data-midi-code="37" ng-class="{ active: isActive(37), mapped: isMapped(37) }">B</span><span class="midi-editor-pad-label  mapped" data-midi-code="36" ng-class="{ active: isActive(36), mapped: isMapped(36) }">V</span></div> </div> </div><!----> <!---->        </section>
    </div>

    <script>
        // Global state for MIDI functionality
        let notePreviewEnabled = true;
        let selectedNotes = []; // Array to store selected MIDI note elements
        let noteIdCounter = 1;
        let isDragging = false;
        let isResizing = false;
        let dragStartX = 0;
        let dragStartY = 0;
        let currentDragNote = null;
        let resizeHandle = null;

        // Toggle note preview functionality
        function toggleNotePreview() {
            const button = document.getElementById('headset-button');
            notePreviewEnabled = !notePreviewEnabled;

            // Update button state
            button.setAttribute('aria-pressed', notePreviewEnabled.toString());

            // Visual feedback
            if (notePreviewEnabled) {
                button.style.opacity = '1';
                button.style.backgroundColor = '';
                console.log('Note preview enabled - MIDI notes will play');
            } else {
                button.style.opacity = '0.5';
                button.style.backgroundColor = '#f5f5f5';
                console.log('Note preview disabled - MIDI notes will not play');
            }
        }

        // Apply legato to selected notes
        function applyLegato() {
            const button = document.getElementById('legato-button');

            if (selectedNotes.length < 2) {
                alert('Please select at least 2 notes to apply legato');
                return;
            }

            // Visual feedback for button press
            button.classList.add('legato-active');
            setTimeout(() => {
                button.classList.remove('legato-active');
            }, 200);

            // Sort notes by start position (left position)
            const sortedNotes = [...selectedNotes].sort((a, b) => {
                return parseInt(a.style.left) - parseInt(b.style.left);
            });

            // Apply legato: make each note end exactly when the next note starts
            for (let i = 0; i < sortedNotes.length - 1; i++) {
                const currentNote = sortedNotes[i];
                const nextNote = sortedNotes[i + 1];

                const currentLeft = parseInt(currentNote.style.left);
                const nextLeft = parseInt(nextNote.style.left);
                const newWidth = nextLeft - currentLeft;

                // Ensure minimum width
                if (newWidth > 20) {
                    currentNote.style.width = newWidth + 'px';
                    console.log(`Applied legato: Note ${i + 1} width changed to ${newWidth}px`);
                }
            }

            console.log(`Legato applied to ${selectedNotes.length} selected notes`);
        }

        // Function to play MIDI note (placeholder for actual implementation)
        function playMidiNote(pitch, velocity = 100) {
            if (!notePreviewEnabled) {
                return; // Don't play if preview is disabled
            }

            console.log(`Playing MIDI note: ${pitch} with velocity: ${velocity}`);
            // In a real implementation, this would use Web Audio API or Tone.js
            // Example: synth.triggerAttackRelease(pitch, "8n");
        }

        // Create a new MIDI note element
        function createNoteElement(x, y, width = 100, pitch = 'C4') {
            const note = document.createElement('div');
            note.className = 'demo-midi-note';
            note.id = `note-${noteIdCounter++}`;
            note.style.left = x + 'px';
            note.style.top = y + 'px';
            note.style.width = width + 'px';
            note.dataset.pitch = pitch;
            note.dataset.velocity = '100';

            // Add resize handles
            const leftHandle = document.createElement('div');
            leftHandle.className = 'resize-handle left';
            note.appendChild(leftHandle);

            const rightHandle = document.createElement('div');
            rightHandle.className = 'resize-handle right';
            note.appendChild(rightHandle);

            // Add event listeners
            note.addEventListener('mousedown', handleNoteMouseDown);
            leftHandle.addEventListener('mousedown', handleResizeStart);
            rightHandle.addEventListener('mousedown', handleResizeStart);

            return note;
        }

        // Handle note selection and dragging
        function handleNoteMouseDown(e) {
            e.preventDefault();
            e.stopPropagation();

            const note = e.currentTarget;

            // Check if clicking on resize handle
            if (e.target.classList.contains('resize-handle')) {
                return; // Let resize handler deal with it
            }

            // Handle selection
            if (!e.ctrlKey && !e.metaKey) {
                // Clear previous selection if not holding Ctrl/Cmd
                clearSelection();
            }

            toggleNoteSelection(note);

            // Start dragging
            isDragging = true;
            currentDragNote = note;
            dragStartX = e.clientX - parseInt(note.style.left);
            dragStartY = e.clientY - parseInt(note.style.top);

            // Play note if preview enabled
            playMidiNote(note.dataset.pitch, parseInt(note.dataset.velocity));
        }

        // Handle resize start
        function handleResizeStart(e) {
            e.preventDefault();
            e.stopPropagation();

            isResizing = true;
            resizeHandle = e.target;
            currentDragNote = e.target.parentElement;
            dragStartX = e.clientX;

            // Clear selection and select only this note
            clearSelection();
            toggleNoteSelection(currentDragNote);
        }

        // Toggle note selection
        function toggleNoteSelection(note) {
            const isSelected = note.classList.contains('selected');

            if (isSelected) {
                note.classList.remove('selected');
                selectedNotes = selectedNotes.filter(n => n !== note);
            } else {
                note.classList.add('selected');
                selectedNotes.push(note);
            }

            updateSelectedCount();
        }

        // Clear all selections
        function clearSelection() {
            selectedNotes.forEach(note => {
                note.classList.remove('selected');
            });
            selectedNotes = [];
            updateSelectedCount();
        }

        // Update selected count display
        function updateSelectedCount() {
            const countElement = document.getElementById('selected-count');
            if (countElement) {
                countElement.textContent = selectedNotes.length;
            }
        }
</body>
</html>ing, 'is-velocity': midiEditorState.isVelocity }" midi-editor-pencil="" midi-editor-area-selection="" ng-if="midiEditorState.isVisible" action-shortcut="actionShortcut" get-view-mode="getViewMode" get-prefer-sharps="getPreferSharps" save-prefer-sharps="savePreferSharps"> <div data-v-app=""><div class="midi-editor-piano-roll-rows"><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white is-root"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="white"></div><div class="black"></div><div class="white"></div><div class="black"></div><div class="white"></div></div><svg width="0" height="0"><defs><pattern id="midi-editor-piano-roll-in-key-highlights" x="0" y="0" height="1280" width="100" patternUnits="userSpaceOnUse"><rect class="" height="10" y="0" x="0" width="2496000"></rect><rect class="" height="10" y="10" x="0" width="2496000"></rect><rect class="" height="10" y="20" x="0" width="2496000"></rect><rect class="" height="10" y="30" x="0" width="2496000"></rect><rect class="" height="10" y="40" x="0" width="2496000"></rect><rect class="" height="10" y="50" x="0" width="2496000"></rect><rect class="" height="10" y="60" x="0" width="2496000"></rect><rect class="" height="10" y="70" x="0" width="2496000"></rect><rect class="" height="10" y="80" x="0" width="2496000"></rect><rect class="" height="10" y="90" x="0" width="2496000"></rect><rect class="" height="10" y="100" x="0" width="2496000"></rect><rect class="" height="10" y="110" x="0" width="2496000"></rect><rect class="" height="10" y="120" x="0" width="2496000"></rect><rect class="" height="10" y="130" x="0" width="2496000"></rect><rect class="" height="10" y="140" x="0" width="2496000"></rect><rect class="" height="10" y="150" x="0" width="2496000"></rect><rect class="" height="10" y="160" x="0" width="2496000"></rect><rect class="" height="10" y="170" x="0" width="2496000"></rect><rect class="" height="10" y="180" x="0" width="2496000"></rect><rect class="" height="10" y="190" x="0" width="2496000"></rect><rect class="" height="10" y="200" x="0" width="2496000"></rect><rect class="" height="10" y="210" x="0" width="2496000"></rect><rect class="" height="10" y="220" x="0" width="2496000"></rect><rect class="" height="10" y="230" x="0" width="2496000"></rect><rect class="" height="10" y="240" x="0" width="2496000"></rect><rect class="" height="10" y="250" x="0" width="2496000"></rect><rect class="" height="10" y="260" x="0" width="2496000"></rect><rect class="" height="10" y="270" x="0" width="2496000"></rect><rect class="" height="10" y="280" x="0" width="2496000"></rect><rect class="" height="10" y="290" x="0" width="2496000"></rect><rect class="" height="10" y="300" x="0" width="2496000"></rect><rect class="" height="10" y="310" x="0" width="2496000"></rect><rect class="" height="10" y="320" x="0" width="2496000"></rect><rect class="" height="10" y="330" x="0" width="2496000"></rect><rect class="" height="10" y="340" x="0" width="2496000"></rect><rect class="" height="10" y="350" x="0" width="2496000"></rect><rect class="" height="10" y="360" x="0" width="2496000"></rect><rect class="" height="10" y="370" x="0" width="2496000"></rect><rect class="" height="10" y="380" x="0" width="2496000"></rect><rect class="" height="10" y="390" x="0" width="2496000"></rect><rect class="" height="10" y="400" x="0" width="2496000"></rect><rect class="" height="10" y="410" x="0" width="2496000"></rect><rect class="" height="10" y="420" x="0" width="2496000"></rect><rect class="" height="10" y="430" x="0" width="2496000"></rect><rect class="" height="10" y="440" x="0" width="2496000"></rect><rect class="" height="10" y="450" x="0" width="2496000"></rect><rect class="" height="10" y="460" x="0" width="2496000"></rect><rect class="" height="10" y="470" x="0" width="2496000"></rect><rect class="" height="10" y="480" x="0" width="2496000"></rect><rect class="" height="10" y="490" x="0" width="2496000"></rect><rect class="" height="10" y="500" x="0" width="2496000"></rect><rect class="" height="10" y="510" x="0" width="2496000"></rect><rect class="" height="10" y="520" x="0" width="2496000"></rect><rect class="" height="10" y="530" x="0" width="2496000"></rect><rect class="" height="10" y="540" x="0" width="2496000"></rect><rect class="" height="10" y="550" x="0" width="2496000"></rect><rect class="" height="10" y="560" x="0" width="2496000"></rect><rect class="" height="10" y="570" x="0" width="2496000"></rect><rect class="" height="10" y="580" x="0" width="2496000"></rect><rect class="" height="10" y="590" x="0" width="2496000"></rect><rect class="" height="10" y="600" x="0" width="2496000"></rect><rect class="" height="10" y="610" x="0" width="2496000"></rect><rect class="" height="10" y="620" x="0" width="2496000"></rect><rect class="" height="10" y="630" x="0" width="2496000"></rect><rect class="" height="10" y="640" x="0" width="2496000"></rect><rect class="" height="10" y="650" x="0" width="2496000"></rect><rect class="" height="10" y="660" x="0" width="2496000"></rect><rect class="" height="10" y="670" x="0" width="2496000"></rect><rect class="" height="10" y="680" x="0" width="2496000"></rect><rect class="" height="10" y="690" x="0" width="2496000"></rect><rect class="" height="10" y="700" x="0" width="2496000"></rect><rect class="" height="10" y="710" x="0" width="2496000"></rect><rect class="" height="10" y="720" x="0" width="2496000"></rect><rect class="" height="10" y="730" x="0" width="2496000"></rect><rect class="" height="10" y="740" x="0" width="2496000"></rect><rect class="" height="10" y="750" x="0" width="2496000"></rect><rect class="" height="10" y="760" x="0" width="2496000"></rect><rect class="" height="10" y="770" x="0" width="2496000"></rect><rect class="" height="10" y="780" x="0" width="2496000"></rect><rect class="" height="10" y="790" x="0" width="2496000"></rect><rect class="" height="10" y="800" x="0" width="2496000"></rect><rect class="" height="10" y="810" x="0" width="2496000"></rect><rect class="" height="10" y="820" x="0" width="2496000"></rect><rect class="" height="10" y="830" x="0" width="2496000"></rect><rect class="" height="10" y="840" x="0" width="2496000"></rect><rect class="" height="10" y="850" x="0" width="2496000"></rect><rect class="" height="10" y="860" x="0" width="2496000"></rect><rect class="" height="10" y="870" x="0" width="2496000"></rect><rect class="" height="10" y="880" x="0" width="2496000"></rect><rect class="" height="10" y="890" x="0" width="2496000"></rect><rect class="" height="10" y="900" x="0" width="2496000"></rect><rect class="" height="10" y="910" x="0" width="2496000"></rect><rect class="" height="10" y="920" x="0" width="2496000"></rect><rect class="" height="10" y="930" x="0" width="2496000"></rect><rect class="" height="10" y="940" x="0" width="2496000"></rect><rect class="" height="10" y="950" x="0" width="2496000"></rect><rect class="" height="10" y="960" x="0" width="2496000"></rect><rect class="" height="10" y="970" x="0" width="2496000"></rect><rect class="" height="10" y="980" x="0" width="2496000"></rect><rect class="" height="10" y="990" x="0" width="2496000"></rect><rect class="" height="10" y="1000" x="0" width="2496000"></rect><rect class="" height="10" y="1010" x="0" width="2496000"></rect><rect class="" height="10" y="1020" x="0" width="2496000"></rect><rect class="" height="10" y="1030" x="0" width="2496000"></rect><rect class="" height="10" y="1040" x="0" width="2496000"></rect><rect class="" height="10" y="1050" x="0" width="2496000"></rect><rect class="" height="10" y="1060" x="0" width="2496000"></rect><rect class="" height="10" y="1070" x="0" width="2496000"></rect><rect class="" height="10" y="1080" x="0" width="2496000"></rect><rect class="" height="10" y="1090" x="0" width="2496000"></rect><rect class="" height="10" y="1100" x="0" width="2496000"></rect><rect class="" height="10" y="1110" x="0" width="2496000"></rect><rect class="" height="10" y="1120" x="0" width="2496000"></rect><rect class="" height="10" y="1130" x="0" width="2496000"></rect><rect class="" height="10" y="1140" x="0" width="2496000"></rect><rect class="" height="10" y="1150" x="0" width="2496000"></rect><rect class="" height="10" y="1160" x="0" width="2496000"></rect><rect class="" height="10" y="1170" x="0" width="2496000"></rect><rect class="" height="10" y="1180" x="0" width="2496000"></rect><rect class="" height="10" y="1190" x="0" width="2496000"></rect><rect class="" height="10" y="1200" x="0" width="2496000"></rect><rect class="" height="10" y="1210" x="0" width="2496000"></rect><rect class="" height="10" y="1220" x="0" width="2496000"></rect><rect class="" height="10" y="1230" x="0" width="2496000"></rect><rect class="" height="10" y="1240" x="0" width="2496000"></rect><rect class="" height="10" y="1250" x="0" width="2496000"></rect><rect class="" height="10" y="1260" x="0" width="2496000"></rect><rect class="" height="10" y="1270" x="0" width="2496000"></rect></pattern></defs></svg></div> <svg class="mix-editor-grid-piano" width="2496000" ng-attr-height="{{ 128 * getNoteHeight() }}" height="1280"> <rect fill="url(#midi-editor-track-grid-pattern)" x="0" y="0" width="2496000" ng-attr-height="{{ 128 * getNoteHeight() + 30 }}" style="transform: translateY(-30px)" height="1310"></rect> </svg> <svg class="mix-editor-grid-drum" width="2496000" height="609"> <defs> <pattern id="midi-editor-drum-pattern" x="0" y="0" width="1" ng-attr-height="{{ 2 * midiEditorState.MIDI_NOTE_UI_HEIGHT_LARGE }}" patternUnits="userSpaceOnUse" height="64"> <rect width="2496000" class="black" x="0" ng-attr-y="0" y="0"></rect> <rect width="2496000" class="white" x="0" ng-attr-y="{{ midiEditorState.MIDI_NOTE_UI_HEIGHT_LARGE }}" y="32"></rect> </pattern> </defs> <rect fill="url(#midi-editor-drum-pattern)" x="0" y="0" width="2496000" height="1200"></rect> <rect fill="url(#midi-editor-track-grid-pattern)" x="0" y="0" width="2496000" height="1240"></rect> </svg> <svg class="mix-editor-grid-pad" width="2496000" height="545"> <defs> <pattern id="midi-editor-pad-pattern" x="0" y="0" width="1" ng-attr-height="{{ 2 * midiEditorState.MIDI_NOTE_UI_HEIGHT_LARGE }}" patternUnits="userSpaceOnUse" height="64"> <rect width="2496000" class="black" x="0" ng-attr-y="0" y="0"></rect> <rect width="2496000" class="white" x="0" ng-attr-y="{{ midiEditorState.MIDI_NOTE_UI_HEIGHT_LARGE }}" y="32"></rect> </pattern> </defs> <rect fill="url(#midi-editor-pad-pattern)" x="0" y="0" width="2496000" height="1200"></rect> <rect fill="url(#midi-editor-track-grid-pattern)" x="0" y="0" width="2496000" height="1240"></rect> </svg> <!----><div class="midi-editor-region" ng-class="{
    'not-loaded': isRegionCorrupted(),
  }" ng-style="getRegionStyle()" midi-editor-ruler-region="" ng-repeat="region in midiEditorState.track.regions track by region.id" style="box-shadow: rgba(0, 195, 125, 0.06) 1318.17px 0px 0px 0px inset, rgba(0, 0, 0, 0.3) 0px 0px 0px 0px inset; left: 0px; width: 1318.17px;"> <div class="midi-editor-region-patterns"> <!----><svg ng-if="midiEditorState.isPianoRoll" class="midi-editor-region-piano-roll-highlights" height="100%" width="2496000"> <rect fill="url(#midi-editor-piano-roll-in-key-highlights)" x="0" y="0" height="100%" width="2496000"></rect> </svg><!----> <!----> </div> <div class="midi-editor-region-left-handler" move-handler="" on-move-start="backupRegion" on-move="moveRegionLeft" on-move-end="updateTrack"> </div> <div ng-show="getRightHandleType() === 'loop'" class="midi-editor-region-loop-handler ng-hide" move-handler="" on-move-start="backupRegion" on-move="expandLoop" on-move-end="updateTrackResetLoopLength"> </div> <div ng-show="getExtraLoopDuration() &gt; 0" class="midi-editor-region-middle-handler ng-hide" ng-style="{ left: getFirstLoopDuration() * midiEditorState.scalePx - 6 + 'px' }" move-handler="" on-move-start="backupRegion" on-move="resizingFirstLoop" on-move-end="updateTrack" style="left: 1312.17px;"> </div> <div ng-show="getRightHandleType() === 'trim'" class="midi-editor-region-right-handler" move-handler="" on-move-start="backupRegion" on-move="resizingRight" on-move-end="updateTrackResetLoopLength"> </div> <!----><div class="midi-editor-note is-selected" ng-class="{
      'is-selected': isSelected(),
      loop: note.inLoop
    }" ng-style="{ 'background-color': getNoteColor(getNote().velocity, getRegionColor()), bottom: getNotePositionY(getNote().midiNote), left: getNote().time * midiEditorState.scalePx + 'px', width: getNote().duration * midiEditorState.scalePx + 'px' }" ng-repeat="note in notes track by getNoteUniqueIndex(note)" style="background-color: rgba(0, 195, 125, 0.79); bottom: 600px; left: 0px; width: 82.3858px;"> <div class="midi-editor-note-velocity" ng-style="{ width: 'calc(' + getNote().velocity * 100 + '% - 8px)' }" style="width: calc(78.7402% - 8px);"> </div> <!----> <!----><div ng-if="!note.inLoop" midi-editor-note-left="" allow-action="editRegion"></div><!----> <!----><div ng-if="!note.inLoop" midi-editor-note-move="" draggable="false"></div><!----> <!----><div ng-if="!note.inLoop" midi-editor-note-right="" allow-action="editRegion"></div><!----> <!----><context-menu ng-if="!note.inLoop" template="https://www.bandlab.com/web-app/midi/partials/_midi-editor-note-context-menu-50472975e.html"></context-menu><!----> </div><!----> </div><!----> <context-menu template="https://www.bandlab.com/web-app/midi/partials/_midi-editor-context-menu-26e0c0054.html"></context-menu> </div><!----> <!----> </div> <!----><svg ng-if="midiEditorState.isVisible &amp;&amp; midiEditorState.isPianoRoll" class="mix-editor-grid-piano-placeholder" width="2496000" height="100%"> <rect fill="url(#midi-editor-track-grid-pattern)" x="0" y="0" width="2496000"></rect> </svg><!----> <div class="midi-editor-scrollbar-horizontal-container" progress="" progress-on-start="backupPositionX" progress-on-move="scrollX"> <div class="midi-editor-scrollbar-horizontal" style="width: 40px; transform: translateX(0px);"></div> </div> <div id="midi-roll-row-select"><div class="midi-roll-row-select-rect"></div></div> </div> <!----><div class="midi-editor-scrollbar-vertical-container" progress="vertical" progress-on-start="backupPositionY" progress-on-move="scrollY" ng-if="midiEditorState.isVisible"> <div class="midi-editor-scrollbar-vertical" style="height: 62px; transform: translateY(107px);"></div> </div><!----> <div class="studio-ruler-buttons" ng-class="{ 'is-pushed-left': state.currentActiveTab }"> <button class="studio-ruler-button" ng-click="initZoomTimer({ state: midiEditorState }); zoomOut({ state: midiEditorState })" ng-disabled="state.isRecording || midiEditorState.scale &lt;= 1" title="Zoom Out"><svg width="18" height="18" viewBox="0 0 18 18" name="zoom-out" size="18" data-v-app=""><svg width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="zoom-out"><path fill="currentColor" d="M7 11h6V9H7z"></path><path fill="currentColor" fill-rule="evenodd" d="M1 10a9 9 0 1 1 18 0 9 9 0 0 1-18 0m9-7a7 7 0 1 0 0 14 7 7 0 0 0 0-14" clip-rule="evenodd"></path><path fill="currentColor" d="m18.7 17.3 4 4-1.4 1.4-4-4z"></path></svg></svg></button> <button class="studio-ruler-button" ng-click="initZoomTimer({ state: midiEditorState }); zoomIn({ state: midiEditorState })" ng-disabled="state.isRecording || midiEditorState.scale &gt;= 640" title="Zoom In"><svg width="18" height="18" viewBox="0 0 18 18" name="zoom-in" size="18" data-v-app=""><svg width="18" height="18" viewBox="0 0 24 24" fill="none" aria-hidden="true" name="zoom-in"><path fill="currentColor" d="M9 11H6V9h3V6h2v3h3v2h-3v3H9z"></path><path fill="currentColor" fill-rule="evenodd" d="M10 1a9 9 0 1 0 0 18 9 9 0 0 0 0-18m-7 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0" clip-rule="evenodd"></path><path fill="currentColor" d="m22.7 21.3-4-4-1.4 1.4 4 4z"></path></svg></svg></button> </div> </section>