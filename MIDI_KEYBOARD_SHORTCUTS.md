# MIDI Editor Keyboard Shortcuts for Scale Degree Playing

## Overview

The MIDI editor now supports playing scale degrees using keyboard shortcuts for **two full octaves** (14 notes total). This allows you to quickly play notes in a selected key signature and octave.

## How to Use

### 1. Select Key Signature
- **Root Note**: Choose from C, C#, D, D#, E, F, F#, G, G#, A, A#, B
- **Mode**: Choose between Major or Minor

### 2. Select Octave
- Choose an octave from 0-8 (default is 4)
- This determines the starting octave for the root note (degree 1)
- Other scale degrees may be in higher octaves if the scale crosses octave boundaries

### 3. Play Scale Degrees
**Hold down** the following keys to play scale degrees across two octaves:
- Notes play continuously while the key is held down
- Notes stop when the key is released
- Keyboard repeat is prevented (no repeated triggering)

#### First Octave (Keys 1-7)
| Key | Scale Degree | Major Example (C Major) | Minor Example (A Minor) |
|-----|--------------|-------------------------|-------------------------|
| 1   | Root         | C                       | A                       |
| 2   | Second       | D                       | B                       |
| 3   | Third        | E (Major 3rd)           | C (Minor 3rd)           |
| 4   | Fourth       | F                       | D                       |
| 5   | Fifth        | G                       | E                       |
| 6   | Sixth        | A (Major 6th)           | F (Minor 6th)           |
| 7   | Seventh      | B (Major 7th)           | G (Minor 7th)           |

#### Second Octave (Keys 8,9,0,-,=,],\)
| Key | Scale Degree | Major Example (C Major) | Minor Example (A Minor) |
|-----|--------------|-------------------------|-------------------------|
| 8   | Root (8th)   | C (next octave)         | A (next octave)         |
| 9   | Second (9th) | D (next octave)         | B (next octave)         |
| 0   | Third (10th) | E (next octave)         | C (next octave)         |
| -   | Fourth (11th)| F (next octave)         | D (next octave)         |
| =   | Fifth (12th) | G (next octave)         | E (next octave)         |
| ]   | Sixth (13th) | A (next octave)         | F (next octave)         |
| \   | Seventh (14th)| B (next octave)        | G (next octave)         |

## Examples

### C Major, Octave 4
- Key 1: C4 (MIDI 60)
- Key 2: D4 (MIDI 62)
- Key 3: E4 (MIDI 64)
- Key 4: F4 (MIDI 65)
- Key 5: G4 (MIDI 67)
- Key 6: A4 (MIDI 69)
- Key 7: B4 (MIDI 71)

### G Major, Octave 5 (Full 14-note example)
**First Octave:**
- Key 1: G5 (MIDI 79)  - Root note in specified octave
- Key 2: A5 (MIDI 81)  - Continues chromatically
- Key 3: B5 (MIDI 83)  - Still in same octave
- Key 4: C6 (MIDI 84)  - Crosses to next octave
- Key 5: D6 (MIDI 86)  - Continues in higher octave
- Key 6: E6 (MIDI 88)  - Continues in higher octave
- Key 7: F#6 (MIDI 90) - Continues in higher octave

**Second Octave:**
- Key 8: G6 (MIDI 91)  - Root note, second octave
- Key 9: A6 (MIDI 93)  - Second degree, second octave
- Key 0: B6 (MIDI 95)  - Third degree, second octave
- Key -: C7 (MIDI 96)  - Fourth degree, second octave
- Key =: D7 (MIDI 98)  - Fifth degree, second octave
- Key ]: E7 (MIDI 100) - Sixth degree, second octave
- Key \: F#7 (MIDI 102)- Seventh degree, second octave

### A Minor, Octave 4
- Key 1: A4 (MIDI 69)
- Key 2: B4 (MIDI 71)
- Key 3: C5 (MIDI 72)
- Key 4: D5 (MIDI 74)
- Key 5: E5 (MIDI 76)
- Key 6: F5 (MIDI 77)
- Key 7: G5 (MIDI 79)

## Visual Feedback

The MIDI editor header displays the current scale notes that will be played when you press the keyboard keys. This updates automatically when you change the key signature or octave. Each note shows its key binding (e.g., "1:G5", "8:G6") for easy reference.

## Technical Details

- Notes are played using the Web Audio API through the audioSynth utility
- The velocity is controlled by the current velocity setting in the MIDI editor
- Notes play with **indefinite duration** - they continue until manually stopped
- Keyboard repeat is prevented to avoid multiple note triggers
- Notes automatically stop when keys are released or when the window loses focus
- The implementation uses standard music theory intervals for major and minor scales
- **Fixed:** Notes no longer auto-stop after 0.5 seconds - they play until key release

## Keyboard Shortcuts Summary

| Key | Action |
|-----|--------|
| 1-7 | Play scale degrees 1-7 (first octave) |
| 8,9,0,-,=,],\ | Play scale degrees 8-14 (second octave) |
| Shift + + | Increase octave |
| Shift + - | Decrease octave |
| R   | Set reference time |
| V   | Toggle velocity mode |
| A   | Toggle add note mode |
| F   | Toggle free movement mode |
| M   | Toggle metronome |
| Arrow keys | Move selected notes |
| Delete/Backspace | Delete selected notes |

## Future Enhancements

Potential future improvements could include:
- Support for other scale types (dorian, mixolydian, etc.)
- Chord playing shortcuts
- Custom key mappings
- Visual highlighting of played notes on the piano roll
