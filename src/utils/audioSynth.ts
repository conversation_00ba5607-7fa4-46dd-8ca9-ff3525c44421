/**
 * Audio synthesis utilities for MIDI note playback
 */

export interface SynthOptions {
  waveform?: OscillatorType;
  attack?: number;
  decay?: number;
  sustain?: number;
  release?: number;
  volume?: number;
}

export interface PlayingNote {
  oscillator: OscillatorNode;
  gain: GainNode;
  startTime: number;
  timeoutId?: number; // Track timeout for automatic stopping
}

export class AudioSynth {
  private audioContext: AudioContext | null = null;
  private activeNotes: Map<number, PlayingNote> = new Map();
  private masterGain: GainNode | null = null;

  constructor() {
    this.initializeAudioContext();
  }

  private initializeAudioContext() {
    if (typeof window !== 'undefined' && !this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);
      this.masterGain.gain.setValueAtTime(0.3, this.audioContext.currentTime); // Master volume
    }
  }

  private ensureAudioContext(): boolean {
    if (!this.audioContext) {
      this.initializeAudioContext();
    }
    
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume().catch(console.error);
    }
    
    return this.audioContext !== null && this.masterGain !== null;
  }

  /**
   * Convert MIDI note number to frequency
   */
  private midiToFrequency(midiNote: number): number {
    return 440 * Math.pow(2, (midiNote - 69) / 12);
  }

  /**
   * Play a MIDI note with optional duration
   * If duration is undefined, the note plays indefinitely until manually stopped
   * If duration is null, the note plays with a default sustain time
   * If duration is a number, the note plays for that duration
   */
  playNote(
    midiNote: number,
    duration?: number | null,
    velocity: number = 80,
    options: SynthOptions = {}
  ): void {
    if (!this.ensureAudioContext() || !this.audioContext || !this.masterGain) {
      return;
    }

    // Stop any existing note at this pitch
    this.stopNote(midiNote);

    try {
      const frequency = this.midiToFrequency(midiNote);
      const currentTime = this.audioContext.currentTime;
      
      // Create oscillator and gain nodes
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();
      
      // Configure oscillator with a more musical waveform
      oscillator.type = options.waveform || 'triangle';
      oscillator.frequency.setValueAtTime(frequency, currentTime);
      
      // Configure ADSR envelope
      const attack = options.attack || 0.01;
      const decay = options.decay || 0.1;
      const sustain = options.sustain || 0.7;
      const release = options.release || 0.3;
      const volume = (options.volume || 0.5) * (velocity / 127);
      
      // Set up gain envelope
      gainNode.gain.setValueAtTime(0, currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, currentTime + attack);
      gainNode.gain.linearRampToValueAtTime(volume * sustain, currentTime + attack + decay);
      
      // Connect nodes
      oscillator.connect(gainNode);
      gainNode.connect(this.masterGain);
      
      // Start oscillator
      oscillator.start(currentTime);
      
      // Handle different duration modes and track timeouts
      let timeoutId: number | undefined;

      if (duration === undefined) {
        // Indefinite duration - note plays until manually stopped
        // Do nothing, let it play indefinitely
      } else if (duration === null) {
        // Default sustain time for clicks
        const sustainTime = 0.5;
        timeoutId = window.setTimeout(() => {
          this.stopNote(midiNote);
        }, sustainTime * 1000);
      } else if (duration > 0) {
        // Specific duration
        timeoutId = window.setTimeout(() => {
          this.stopNote(midiNote);
        }, duration * 1000);
      }

      // Store the playing note with timeout ID
      this.activeNotes.set(midiNote, {
        oscillator,
        gain: gainNode,
        startTime: currentTime,
        timeoutId
      });
      
    } catch (error) {
      console.error('Error playing note:', error);
    }
  }

  /**
   * Stop a playing MIDI note
   */
  stopNote(midiNote: number): void {
    const playingNote = this.activeNotes.get(midiNote);
    if (!playingNote || !this.audioContext) {
      return;
    }

    try {
      // Cancel any pending timeout to prevent double-stopping
      if (playingNote.timeoutId !== undefined) {
        window.clearTimeout(playingNote.timeoutId);
      }

      const currentTime = this.audioContext.currentTime;
      const release = 0.1; // Release time

      // Fade out
      playingNote.gain.gain.cancelScheduledValues(currentTime);
      playingNote.gain.gain.setValueAtTime(playingNote.gain.gain.value, currentTime);
      playingNote.gain.gain.linearRampToValueAtTime(0, currentTime + release);

      // Stop oscillator
      playingNote.oscillator.stop(currentTime + release);

      // Remove from active notes
      this.activeNotes.delete(midiNote);

    } catch (error) {
      console.error('Error stopping note:', error);
    }
  }

  /**
   * Stop all playing notes
   */
  stopAllNotes(): void {
    // Create a copy of the keys to avoid modification during iteration
    const notesToStop = Array.from(this.activeNotes.keys());
    notesToStop.forEach(midiNote => {
      this.stopNote(midiNote);
    });
  }

  /**
   * Get count of active notes for debugging
   */
  getActiveNotesCount(): number {
    return this.activeNotes.size;
  }

  /**
   * Set master volume (0-1)
   */
  setMasterVolume(volume: number): void {
    if (this.masterGain && this.audioContext) {
      this.masterGain.gain.setValueAtTime(
        Math.max(0, Math.min(1, volume)), 
        this.audioContext.currentTime
      );
    }
  }

  /**
   * Get audio context for external use
   */
  getAudioContext(): AudioContext | null {
    return this.audioContext;
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.stopAllNotes();
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().catch(console.error);
    }
    this.audioContext = null;
    this.masterGain = null;
  }
}

// Create a singleton instance for the app
export const audioSynth = new AudioSynth();
