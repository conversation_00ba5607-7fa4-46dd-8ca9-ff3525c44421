/**
 * Music theory utilities for scale generation and note calculations
 */

// Note names in chromatic order
export const NOTE_NAMES = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

// Alternative note names with flats
export const NOTE_NAMES_FLAT = ['C', 'Db', 'D', 'Eb', 'E', 'F', 'Gb', 'G', 'Ab', 'A', 'Bb', 'B'];

// Scale patterns (intervals in semitones from root)
export const SCALE_PATTERNS = {
  major: [0, 2, 4, 5, 7, 9, 11], // W-W-H-W-W-W-H
  minor: [0, 2, 3, 5, 7, 8, 10], // W-H-W-W-H-W-W (natural minor)
} as const;

export type ScaleMode = keyof typeof SCALE_PATTERNS;

/**
 * Convert note name to semitone offset from C
 */
export function noteNameToSemitone(noteName: string): number {
  // Handle both sharp and flat notation
  const index = NOTE_NAMES.indexOf(noteName);
  if (index !== -1) return index;
  
  const flatIndex = NOTE_NAMES_FLAT.indexOf(noteName);
  if (flatIndex !== -1) return flatIndex;
  
  throw new Error(`Invalid note name: ${noteName}`);
}

/**
 * Convert MIDI note number to note name
 */
export function midiNoteToNoteName(midiNote: number): string {
  const octave = Math.floor(midiNote / 12) - 1;
  const noteIndex = midiNote % 12;
  return `${NOTE_NAMES[noteIndex]}${octave}`;
}

/**
 * Convert note name and octave to MIDI note number
 */
export function noteNameAndOctaveToMidi(noteName: string, octave: number): number {
  const semitone = noteNameToSemitone(noteName);
  return (octave + 1) * 12 + semitone;
}

/**
 * Get scale degrees for a given key and mode
 */
export function getScaleDegrees(rootNote: string, mode: ScaleMode): number[] {
  const rootSemitone = noteNameToSemitone(rootNote);
  const pattern = SCALE_PATTERNS[mode];
  
  return pattern.map(interval => (rootSemitone + interval) % 12);
}

/**
 * Get MIDI note number for a scale degree starting from a specific octave
 * The octave parameter specifies the octave of the root note (degree 1)
 * Other degrees may be in higher octaves if they wrap around
 * Supports degrees 1-14 (two full octaves)
 */
export function getScaleDegreeMidiNote(
  rootNote: string,
  mode: ScaleMode,
  degree: number,
  octave: number
): number {
  if (degree < 1 || degree > 14) {
    throw new Error(`Scale degree must be between 1 and 14, got ${degree}`);
  }

  const rootSemitone = noteNameToSemitone(rootNote);
  const pattern = SCALE_PATTERNS[mode];

  // For degrees 8-14, we need to add an octave and use the pattern again
  let actualDegree: number;
  let octaveOffset: number;

  if (degree <= 7) {
    actualDegree = degree;
    octaveOffset = 0;
  } else {
    actualDegree = degree - 7; // 8 becomes 1, 9 becomes 2, etc.
    octaveOffset = 12; // Add one octave
  }

  const degreeIndex = actualDegree - 1; // Convert to 0-based index
  const intervalFromRoot = pattern[degreeIndex] + octaveOffset;

  // Calculate the absolute semitone position from the root note
  const absoluteSemitone = rootSemitone + intervalFromRoot;

  // Calculate which octave this note falls into
  const noteOctave = octave + Math.floor(absoluteSemitone / 12);
  const noteSemitone = absoluteSemitone % 12;

  return (noteOctave + 1) * 12 + noteSemitone;
}

/**
 * Get note name for a scale degree (supports degrees 1-14)
 */
export function getScaleDegreeNoteName(
  rootNote: string,
  mode: ScaleMode,
  degree: number
): string {
  if (degree < 1 || degree > 14) {
    throw new Error(`Scale degree must be between 1 and 14, got ${degree}`);
  }

  const scaleDegrees = getScaleDegrees(rootNote, mode);

  // For degrees 8-14, use the same pattern as 1-7
  const actualDegree = degree <= 7 ? degree : degree - 7;
  const degreeIndex = actualDegree - 1; // Convert to 0-based index
  const semitone = scaleDegrees[degreeIndex];

  return NOTE_NAMES[semitone];
}

/**
 * Get all available root notes for key selection
 */
export function getAvailableKeys(): string[] {
  return [...NOTE_NAMES];
}

/**
 * Get scale degree names for display
 */
export function getScaleDegreeNames(mode: ScaleMode): string[] {
  if (mode === 'major') {
    return ['1 (Root)', '2 (Major 2nd)', '3 (Major 3rd)', '4 (Perfect 4th)', '5 (Perfect 5th)', '6 (Major 6th)', '7 (Major 7th)'];
  } else {
    return ['1 (Root)', '2 (Major 2nd)', '♭3 (Minor 3rd)', '4 (Perfect 4th)', '5 (Perfect 5th)', '♭6 (Minor 6th)', '♭7 (Minor 7th)'];
  }
}

/**
 * Get the full scale notes for display (7 notes - first octave)
 */
export function getScaleNotes(rootNote: string, mode: ScaleMode, octave: number): Array<{
  degree: number;
  noteName: string;
  noteOctave: number;
  midiNote: number;
  displayName: string;
}> {
  return getExtendedScaleNotes(rootNote, mode, octave, 7);
}

/**
 * Get extended scale notes for display (up to 14 notes - two octaves)
 */
export function getExtendedScaleNotes(rootNote: string, mode: ScaleMode, octave: number, numDegrees: number = 14): Array<{
  degree: number;
  noteName: string;
  noteOctave: number;
  midiNote: number;
  displayName: string;
  keyBinding?: string;
}> {
  const scaleDegreeNames = getScaleDegreeNames(mode);
  const keyBindings = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', ']', '\\'];

  return Array.from({ length: Math.min(numDegrees, 14) }, (_, i) => {
    const degree = i + 1;
    const midiNote = getScaleDegreeMidiNote(rootNote, mode, degree, octave);
    const noteOctave = Math.floor(midiNote / 12) - 1;
    const noteSemitone = midiNote % 12;
    const noteName = NOTE_NAMES[noteSemitone];

    // For degrees 8-14, repeat the scale degree names with "2nd octave" prefix
    let displayName: string;
    if (degree <= 7) {
      displayName = scaleDegreeNames[i];
    } else {
      const baseIndex = (degree - 8);
      displayName = `${scaleDegreeNames[baseIndex]} (2nd octave)`;
    }

    return {
      degree,
      noteName,
      noteOctave,
      midiNote,
      displayName,
      keyBinding: keyBindings[i]
    };
  });
}
