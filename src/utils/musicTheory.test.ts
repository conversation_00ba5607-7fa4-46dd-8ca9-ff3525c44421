/**
 * Test file for music theory utilities
 * Run this in the browser console to verify functionality
 */

import { 
  getScaleDegreeMidiNote, 
  getScaleDegreeNoteName, 
  getScaleNotes,
  noteNameToSemitone,
  midiNoteToNoteName 
} from './musicTheory';

// Test basic note conversion
console.log('=== Testing Note Conversion ===');
console.log('C to semitone:', noteNameToSemitone('C')); // Should be 0
console.log('G to semitone:', noteNameToSemitone('G')); // Should be 7
console.log('F# to semitone:', noteNameToSemitone('F#')); // Should be 6

console.log('MIDI 60 to note name:', midiNoteToNoteName(60)); // Should be C4
console.log('MIDI 67 to note name:', midiNoteToNoteName(67)); // Should be G4

// Test C Major scale
console.log('\n=== Testing C Major Scale ===');
for (let degree = 1; degree <= 7; degree++) {
  const noteName = getScaleDegreeNoteName('C', 'major', degree);
  const midiNote = getScaleDegreeMidiNote('C', 'major', degree, 4);
  console.log(`Degree ${degree}: ${noteName} (MIDI ${midiNote})`);
}

// Test G Major scale - this should show octave crossing
console.log('\n=== Testing G Major Scale (Octave 5) ===');
for (let degree = 1; degree <= 7; degree++) {
  const noteName = getScaleDegreeNoteName('G', 'major', degree);
  const midiNote = getScaleDegreeMidiNote('G', 'major', degree, 5);
  const fullNoteName = midiNoteToNoteName(midiNote);
  console.log(`Degree ${degree}: ${noteName} -> ${fullNoteName} (MIDI ${midiNote})`);
}
// Expected: G5, A5, B5, C6, D6, E6, F#6

// Test A Minor scale
console.log('\n=== Testing A Minor Scale ===');
for (let degree = 1; degree <= 7; degree++) {
  const noteName = getScaleDegreeNoteName('A', 'minor', degree);
  const midiNote = getScaleDegreeMidiNote('A', 'minor', degree, 4);
  console.log(`Degree ${degree}: ${noteName} (MIDI ${midiNote})`);
}

// Test getScaleNotes function
console.log('\n=== Testing getScaleNotes Function ===');
const cMajorNotes = getScaleNotes('C', 'major', 4);
console.log('C Major scale notes (octave 4):', cMajorNotes);

const gMajorNotes = getScaleNotes('G', 'major', 5);
console.log('G Major scale notes (octave 5):', gMajorNotes);
console.log('G Major formatted:', gMajorNotes.map(n => `${n.degree}:${n.noteName}${n.noteOctave}`).join(' '));

export {}; // Make this a module
