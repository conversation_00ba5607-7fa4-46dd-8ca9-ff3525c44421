import { useEffect, useState, useRef } from 'react';
import './App.css';
import YouTubeUrlInput from './components/YouTubeUrlInput';
import YouTubePlayer, { YouTubePlayerRef } from './components/YouTubePlayer';
import TransportControls from './components/TransportControls';
import LoopControls from './components/LoopControls';
import DraggableLyricsPopup from './components/DraggableLyricsPopup';
import { useAudioStore } from './store/useAudioStore';
import { BandLabMidiEditor } from './components/midi-editor';
import { ToastContainer } from 'react-toastify';
import { audioSynth } from './utils/audioSynth';
import { MidiNote } from './types/midi';

function App() {
  const youtubePlayerRef = useRef<YouTubePlayerRef | null>(null);
  const { youtubeUrl, isYoutubeSource } = useAudioStore();
  const [showLyricsPopup, setShowLyricsPopup] = useState(false);
  const { isPlaying, setIsPlaying, loopActive, loopStart, setCurrentTime } = useAudioStore();
  const [midiNotes, setMidiNotes] = useState<MidiNote[]>([]);
  const [midiEditorState, setMidiEditorState] = useState<{
    bpm: number;
    referenceStartTime: number | null;
    selectedKey: string;
    selectedMode: 'major' | 'minor';
    zoom: number;
  }>({
    bpm: 120,
    referenceStartTime: null,
    selectedKey: 'C',
    selectedMode: 'major',
    zoom: 1
  });

  // Test function for audio synthesis
  const testAudioSynth = () => {
    // Play a simple C major scale
    const cMajorScale = [60, 62, 64, 65, 67, 69, 71, 72]; // C4 to C5
    cMajorScale.forEach((note, index) => {
      setTimeout(() => {
        audioSynth.playNote(note, 0.5, 80);
      }, index * 200);
    });
  };

  // Set up keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ignore shortcuts if focus is inside an input or textarea (except for specific lyrics shortcuts handled by the popup itself)
      const targetTagName = (e.target as HTMLElement)?.tagName;
       if (targetTagName === 'INPUT' || targetTagName === 'TEXTAREA') {
           // Allow lyrics popup to handle its own Cmd/Ctrl + B/I/U shortcuts
           if (!(e.metaKey || e.ctrlKey) || !['b', 'i', 'u'].includes(e.key.toLowerCase())) {
             return; // Ignore if focused on input/textarea and not a lyrics shortcut
           }
       }

       if (e.code === 'Space') {
        e.preventDefault();
        if (loopActive) {
          setCurrentTime(loopStart);
          window.dispatchEvent(new CustomEvent('youtube-seek', { detail: { time: loopStart } }));
          // Also dispatch youtube-loop event to ensure MIDI notes are stopped
          window.dispatchEvent(new CustomEvent('youtube-loop', { detail: { time: loopStart } }));
        } else {
          setIsPlaying(!isPlaying);
        }
      } else if (e.key.toLowerCase() === 'p') {
        e.preventDefault();
        setIsPlaying(!isPlaying); // Simple play/pause toggle without loop restart
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown); // Cleanup listener
    };
  }, [isPlaying, loopActive, loopStart, setCurrentTime, setIsPlaying]);

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', display: 'flex', flexDirection: 'column', padding: '1rem' }}>
      <header style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '1.875rem', fontWeight: 'bold', textAlign: 'center', color: '#1f2937' }}>
          LearnSong
        </h1>
        <p style={{ textAlign: 'center', color: '#4b5563', marginTop: '0.5rem' }}>
          Slow down, loop, and transcribe music from YouTube videos
        </p>
      </header>

      <main style={{ flex: 1, maxWidth: '56rem', margin: '0 auto', width: '100%', display: 'flex', flexDirection: 'column' }}>
        <YouTubeUrlInput />

        {isYoutubeSource && youtubeUrl ? (
          <>
            <div style={{ marginBottom: '1.5rem' }}>
              <YouTubePlayer url={youtubeUrl} ref={youtubePlayerRef} />
            </div>
            <TransportControls
              midiNotes={midiNotes}
              midiEditorState={midiEditorState}
            />
            <LoopControls />
            <div style={{ marginTop: '2rem' }}> {/* Added margin for spacing */}
              <BandLabMidiEditor
                getCurrentVideoTime={() => youtubePlayerRef.current?.getCurrentTime()}
                onNotesChange={setMidiNotes}
                onEditorStateChange={setMidiEditorState}
              />
            </div>
          </>
        ) : (
          <div style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div style={{ textAlign: 'center', padding: '2rem', backgroundColor: 'white', borderRadius: '0.375rem', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>Welcome to LearnSong</h2>
              <p style={{ marginBottom: '1rem' }}>Enter a YouTube URL above to get started.</p>
              <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                You can slow down videos, adjust pitch, create loops, and more!
              </p>
            </div>
          </div>
        )}
      </main>

      {/* toast container; auto-dismiss after 3s */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar
        newestOnTop={false}
        closeOnClick
        pauseOnHover
      />

      <footer style={{ marginTop: '2rem', textAlign: 'center', fontSize: '0.875rem', color: '#6b7280' }}>
        <p>LearnSong - A tool for musicians to learn songs</p>
      </footer>

      {/* Conditionally render the lyrics popup */}
      {showLyricsPopup && <DraggableLyricsPopup onClose={() => setShowLyricsPopup(false)} />}
      {/* Button to toggle lyrics popup */}
      <button
        onClick={() => setShowLyricsPopup(!showLyricsPopup)}
        style={{
          position: 'fixed',
          bottom: '1rem',
          right: '1rem',
          padding: '0.5rem 1rem',
          backgroundColor: showLyricsPopup ? '#6c757d' : '#17a2b8',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
        }}
      >
        {showLyricsPopup ? 'Hide Lyrics' : 'Show Lyrics'}
      </button>
    </div>
  );
}

export default App;
