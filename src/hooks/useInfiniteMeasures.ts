import { useState, useCallback, useMemo } from 'react';
import { MeasureState, TimeRange, MidiNote } from '../types/midi';

interface UseInfiniteMeasuresOptions {
  beatsPerMeasure: number;
  pixelsPerBeat: number;
  initialVisibleMeasures: number;
  maxMeasures: number;
  bufferSize: number;
}

interface UseInfiniteMeasuresReturn {
  measureState: MeasureState;
  totalBeats: number;
  gridWidth: number;
  handleScroll: (scrollLeft: number, containerWidth: number) => void;
  getNotesInRange: (notes: MidiNote[], timeRange: TimeRange) => MidiNote[];
  shouldExpandGrid: boolean;
  getVisibleMeasureRange: (scrollLeft: number, containerWidth: number) => { start: number; end: number };
}

/**
 * Custom hook for managing infinite/continuous MIDI editor measures
 * Handles dynamic measure loading, unloading, and grid expansion
 */
export const useInfiniteMeasures = ({
  beatsPerMeasure,
  pixelsPerBeat,
  initialVisibleMeasures = 32,
  maxMeasures = 999,
  bufferSize = 8
}: UseInfiniteMeasuresOptions): UseInfiniteMeasuresReturn => {
  
  const [measureState, setMeasureState] = useState<MeasureState>({
    visibleRange: { start: 0, end: initialVisibleMeasures },
    loadedRange: { start: 0, end: initialVisibleMeasures + bufferSize },
    maxMeasures,
    bufferSize
  });

  // Calculate total beats based on loaded range
  const totalBeats = useMemo(() => {
    return measureState.loadedRange.end * beatsPerMeasure;
  }, [measureState.loadedRange.end, beatsPerMeasure]);

  // Calculate grid width based on total beats
  const gridWidth = useMemo(() => {
    return totalBeats * pixelsPerBeat;
  }, [totalBeats, pixelsPerBeat]);

  // Determine if grid should expand based on current state
  const shouldExpandGrid = useMemo(() => {
    const remainingMeasures = maxMeasures - measureState.loadedRange.end;
    return remainingMeasures > 0;
  }, [measureState.loadedRange.end, maxMeasures]);

  /**
   * Get the visible measure range based on scroll position
   */
  const getVisibleMeasureRange = useCallback((scrollLeft: number, containerWidth: number) => {
    const measureWidth = beatsPerMeasure * pixelsPerBeat;
    const startMeasure = Math.floor(scrollLeft / measureWidth);
    const endMeasure = Math.ceil((scrollLeft + containerWidth) / measureWidth);
    
    return {
      start: Math.max(0, startMeasure),
      end: Math.min(maxMeasures, endMeasure)
    };
  }, [beatsPerMeasure, pixelsPerBeat, maxMeasures]);

  /**
   * Handle scroll events and manage measure loading/unloading
   */
  const handleScroll = useCallback((scrollLeft: number, containerWidth: number) => {
    const visibleRange = getVisibleMeasureRange(scrollLeft, containerWidth);

    setMeasureState(prevState => {
      // Calculate new loaded range with buffer
      const newLoadedStart = Math.max(0, visibleRange.start - bufferSize);
      const newLoadedEnd = Math.min(maxMeasures, visibleRange.end + bufferSize);

      // Always ensure we have at least the initial visible measures loaded
      const minLoadedEnd = Math.max(newLoadedEnd, initialVisibleMeasures);

      // Only update if there's a significant change to avoid unnecessary re-renders
      const loadedStartChanged = Math.abs(newLoadedStart - prevState.loadedRange.start) > bufferSize / 2;
      const loadedEndChanged = Math.abs(minLoadedEnd - prevState.loadedRange.end) > bufferSize / 2;
      const visibleStartChanged = visibleRange.start !== prevState.visibleRange.start;
      const visibleEndChanged = visibleRange.end !== prevState.visibleRange.end;

      if (loadedStartChanged || loadedEndChanged || visibleStartChanged || visibleEndChanged) {
        return {
          ...prevState,
          visibleRange,
          loadedRange: {
            start: newLoadedStart,
            end: minLoadedEnd
          }
        };
      }

      return prevState;
    });
  }, [getVisibleMeasureRange, bufferSize, maxMeasures, initialVisibleMeasures]);

  /**
   * Get notes that fall within a specific time range for efficient rendering
   */
  const getNotesInRange = useCallback((notes: MidiNote[], timeRange: TimeRange): MidiNote[] => {
    return notes.filter(note => {
      const noteEnd = note.startTime + note.duration;
      // Include notes that overlap with the time range
      return note.startTime < timeRange.endBeat && noteEnd > timeRange.startBeat;
    });
  }, []);

  return {
    measureState,
    totalBeats,
    gridWidth,
    handleScroll,
    getNotesInRange,
    shouldExpandGrid,
    getVisibleMeasureRange
  };
};

/**
 * Utility function to convert measure number to beat position
 */
export const measureToBeat = (measure: number, beatsPerMeasure: number): number => {
  return measure * beatsPerMeasure;
};

/**
 * Utility function to convert beat position to measure number
 */
export const beatToMeasure = (beat: number, beatsPerMeasure: number): number => {
  return Math.floor(beat / beatsPerMeasure);
};

/**
 * Utility function to check if a note exists in a specific measure range
 */
export const hasNotesInMeasureRange = (
  notes: MidiNote[],
  startMeasure: number,
  endMeasure: number,
  beatsPerMeasure: number
): boolean => {
  const startBeat = measureToBeat(startMeasure, beatsPerMeasure);
  const endBeat = measureToBeat(endMeasure, beatsPerMeasure);

  return notes.some(note => {
    const noteEnd = note.startTime + note.duration;
    return note.startTime < endBeat && noteEnd > startBeat;
  });
};

/**
 * Utility function to get the furthest measure that contains notes
 */
export const getFurthestNotePosition = (notes: MidiNote[], beatsPerMeasure: number): number => {
  if (notes.length === 0) return 0;

  const furthestBeat = Math.max(...notes.map(note => note.startTime + note.duration));
  return beatToMeasure(furthestBeat, beatsPerMeasure);
};

/**
 * Utility function to optimize loaded range based on note positions
 */
export const optimizeLoadedRange = (
  currentRange: { start: number; end: number },
  visibleRange: { start: number; end: number },
  notes: MidiNote[],
  beatsPerMeasure: number,
  bufferSize: number,
  maxMeasures: number
): { start: number; end: number } => {
  const furthestNotePosition = getFurthestNotePosition(notes, beatsPerMeasure);

  // Don't load beyond the furthest note position + buffer unless we're near the visible area
  const maxUsefulEnd = Math.max(
    furthestNotePosition + bufferSize,
    visibleRange.end + bufferSize
  );

  const optimizedEnd = Math.min(maxUsefulEnd, maxMeasures);
  const optimizedStart = Math.max(0, visibleRange.start - bufferSize);

  return {
    start: optimizedStart,
    end: optimizedEnd
  };
};
