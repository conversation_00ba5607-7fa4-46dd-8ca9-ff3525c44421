import { useCallback, useRef, useEffect } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  frameRate: number;
  lastRenderTimestamp: number;
}

interface UseCanvasPerformanceOptions {
  debounceMs?: number;
  maxCanvasSize?: number;
  enableMetrics?: boolean;
}

export const useCanvasPerformance = (options: UseCanvasPerformanceOptions = {}) => {
  const {
    debounceMs = 16, // ~60fps
    maxCanvasSize = 8192,
    enableMetrics = false
  } = options;

  const metricsRef = useRef<PerformanceMetrics>({
    renderTime: 0,
    frameRate: 60,
    lastRenderTimestamp: 0
  });

  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const rafRef = useRef<number>();
  const lastParamsRef = useRef<string>('');

  // Performance monitoring
  const startRenderTimer = useCallback(() => {
    if (!enableMetrics) return () => {};
    
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      const now = Date.now();
      
      metricsRef.current.renderTime = renderTime;
      
      // Calculate frame rate
      if (metricsRef.current.lastRenderTimestamp > 0) {
        const timeDiff = now - metricsRef.current.lastRenderTimestamp;
        metricsRef.current.frameRate = timeDiff > 0 ? 1000 / timeDiff : 60;
      }
      
      metricsRef.current.lastRenderTimestamp = now;
      
      // Log performance warnings
      if (renderTime > 16.67) { // Slower than 60fps
        console.warn(`Canvas render took ${renderTime.toFixed(2)}ms (target: <16.67ms)`);
      }
    };
  }, [enableMetrics]);

  // Optimized debounced render function
  const debouncedRender = useCallback((
    renderFn: () => void,
    params: string,
    immediate = false
  ) => {
    // Skip if parameters haven't changed
    if (lastParamsRef.current === params && !immediate) {
      return;
    }
    
    lastParamsRef.current = params;

    // Clear existing timeouts/rafs
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    if (immediate) {
      // Immediate render for critical updates
      const endTimer = startRenderTimer();
      renderFn();
      endTimer();
    } else {
      // Debounced render for performance
      debounceTimeoutRef.current = setTimeout(() => {
        rafRef.current = requestAnimationFrame(() => {
          const endTimer = startRenderTimer();
          renderFn();
          endTimer();
        });
      }, debounceMs);
    }
  }, [debounceMs, startRenderTimer]);

  // Canvas size optimization
  const getOptimizedCanvasSize = useCallback((
    width: number,
    height: number,
    zoom: number = 1
  ) => {
    // Limit canvas size to prevent performance issues
    const effectiveWidth = Math.min(width * zoom, maxCanvasSize);
    const effectiveHeight = Math.min(height * zoom, maxCanvasSize);
    
    // Warn if we're hitting limits
    if (width * zoom > maxCanvasSize || height * zoom > maxCanvasSize) {
      console.warn(`Canvas size limited to ${maxCanvasSize}px (requested: ${width * zoom}x${height * zoom})`);
    }
    
    return {
      width: effectiveWidth,
      height: effectiveHeight,
      isLimited: effectiveWidth < width * zoom || effectiveHeight < height * zoom
    };
  }, [maxCanvasSize]);

  // Viewport culling optimization
  const getVisibleBounds = useCallback((
    viewportX: number,
    viewportY: number,
    viewportWidth: number,
    viewportHeight: number,
    itemSize: number,
    totalItems: number,
    buffer: number = 2
  ) => {
    const startIndex = Math.max(0, Math.floor(viewportX / itemSize) - buffer);
    const endIndex = Math.min(
      totalItems,
      Math.ceil((viewportX + viewportWidth) / itemSize) + buffer
    );
    
    return { startIndex, endIndex };
  }, []);

  // Cleanup function
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, []);

  // Get current performance metrics
  const getMetrics = useCallback(() => {
    return { ...metricsRef.current };
  }, []);

  return {
    debouncedRender,
    getOptimizedCanvasSize,
    getVisibleBounds,
    getMetrics,
    startRenderTimer
  };
};
