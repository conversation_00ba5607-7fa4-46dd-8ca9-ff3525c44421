/**
 * Represents a single MIDI note.
 */
export interface MidiNote {
  /** Unique identifier for the note. */
  id: string | number;

  /** MIDI pitch number (e.g., 60 for C4, 61 for C#4). */
  pitch: number;

  /** Start time of the note, typically in beats from the beginning of the sequence. */
  startTime: number;

  /** Duration of the note, typically in beats. */
  duration: number;

  /** Velocity of the note (0-127), representing its loudness. */
  velocity?: number; // Optional, defaults can be handled by the synth

  // Optional: Add other properties like channel, trackId, etc., if needed later
}

/**
 * Represents a sequence of MIDI notes, often corresponding to a track or a clip.
 */
export interface MidiSequence {
  notes: MidiNote[];
  // Optional: Add metadata like time signature, tempo, etc.
  // timeSignature?: { numerator: number; denominator: number };
  // tempoBPM?: number;
}

/**
 * Represents the state of measure management in the infinite MIDI editor
 */
export interface MeasureState {
  /** Currently visible measures range */
  visibleRange: { start: number; end: number };
  /** Loaded measures range (includes buffer) */
  loadedRange: { start: number; end: number };
  /** Maximum number of measures allowed */
  maxMeasures: number;
  /** Buffer size for preloading measures */
  bufferSize: number;
}

/**
 * Represents a time range for efficient note querying
 */
export interface TimeRange {
  startBeat: number;
  endBeat: number;
}
