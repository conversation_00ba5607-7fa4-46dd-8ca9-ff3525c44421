import React, { useRef, useState, useEffect } from 'react';
import Draggable from 'react-draggable'; // Import react-draggable
import { useAudioStore } from '../store/useAudioStore'; // Import the store

interface DraggableLyricsPopupProps {
  initialPosition?: { x: number; y: number };
  onClose?: () => void; // Optional close handler
}

const DraggableLyricsPopup: React.FC<DraggableLyricsPopupProps> = ({ initialPosition, onClose }) => {
  // Position state is now handled by Draggable, but we might keep it if needed for controlled mode
  const nodeRef = useRef<HTMLDivElement>(null); // Create a ref for Draggable, typed correctly
  const [isDragging, setIsDragging] = useState(false); // Track dragging state
  const [isResizing, setIsResizing] = useState(false); // Track resizing state
  const [overlayCursor, setOverlayCursor] =
    useState<'move' | 'ns-resize' | 'ew-resize' | 'nwse-resize'>('move');

  // Get lyrics settings and setters from the store
  const {
    lyricsFontSize, setLyricsFontSize,
    lyricsIsBold, toggleLyricsBold,
    lyricsIsItalic, toggleLyricsItalic,
    lyricsIsUnderlined, toggleLyricsUnderlined,
  } = useAudioStore();

  // Get lyrics state and setter from the store
  const lyrics = useAudioStore((state) => state.lyrics);
  const setLyrics = useAudioStore((state) => state.setLyrics);

  // Combined effect that creates an overlay when dragging or resizing
  useEffect(() => {
    const id = 'lyrics-overlay';
    const need = isDragging || isResizing;

    if (need) {
      let ov = document.getElementById(id) as HTMLDivElement | null;
      if (!ov) {
        ov = document.createElement('div');
        ov.id = id;
        document.body.appendChild(ov);
      }
      Object.assign(ov.style, {
        position: 'fixed',
        inset: '0',
        background: 'transparent',
        zIndex: '9999',
        pointerEvents: 'auto',   // swallow ALL events
        cursor: overlayCursor,
      });
    } else {
      document.getElementById(id)?.remove();
    }
    return () => document.getElementById(id)?.remove();
  }, [isDragging, isResizing, overlayCursor]);

  // Keyboard shortcuts for styling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if Cmd or Ctrl is pressed along with B, I, or U
      const isModifier = e.metaKey || e.ctrlKey;
      const targetIsInput = (e.target as HTMLElement)?.tagName === 'TEXTAREA' || (e.target as HTMLElement)?.tagName === 'INPUT';

      // Only handle shortcuts if the popup is visible
      if (nodeRef.current && document.body.contains(nodeRef.current)) {
         // If the target is the textarea, let the browser handle default Cmd/Ctrl+B/I/U first,
         // but also apply our style toggle if it didn't prevent default (less common in textareas).
         // For simplicity, we'll just handle globally for now and prevent default IF
         // it's one of our specific shortcuts.

        if (isModifier) {
          switch (e.key.toLowerCase()) {
            case 'b':
              toggleLyricsBold();
              e.preventDefault(); // Prevent browser bold if any
              break;
            case 'i':
              toggleLyricsItalic();
              e.preventDefault(); // Prevent browser italic if any
              break;
            case 'u':
              // Check if the target is the textarea to avoid interfering with other inputs
              if (targetIsInput) {
                 toggleLyricsUnderlined();
                 e.preventDefault(); // Prevent browser underline if any
              }
              break;
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleLyricsBold, toggleLyricsItalic, toggleLyricsUnderlined]); // Dependencies: actions

  return (
    <Draggable
      nodeRef={nodeRef as React.RefObject<HTMLElement>} // Use type assertion
      handle=".popup-header" // Specify the drag handle
      defaultPosition={initialPosition || { x: 100, y: 100 }} // Set initial position
      bounds="body" // Allow movement within the entire viewport
      onStart={() => {
        setOverlayCursor('move');
        setIsDragging(true);
      }}
      onStop={() => setIsDragging(false)}
    >
      <div
        ref={nodeRef} // Attach the ref to the element Draggable controls
        style={{
          position: 'fixed', // Use fixed positioning for viewport-relative positioning
          width: '300px',
          border: '1px solid #ccc',
          borderRadius: '8px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          backgroundColor: 'white',
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        {/* Header - Draggable handle */}
        <div
          className="popup-header"
          style={{
            padding: '8px 12px',
            backgroundColor: '#f0f0f0',
            borderBottom: '1px solid #ccc',
            cursor: 'move',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <span style={{ fontWeight: 'bold' }}>Lyrics</span>
          {onClose && (
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '16px',
                cursor: 'pointer',
                padding: '0 4px',
              }}
              aria-label="Close"
              // Prevent dragging when clicking the close button
              onMouseDown={(e) => e.stopPropagation()}
            >
              &times;
            </button>
          )}
        </div>

        {/* Controls Bar (Font Size and Style) */}
        <div style={{
           padding: '4px 12px',
           backgroundColor: '#e9e9e9',
           borderBottom: '1px solid #ccc',
           display: 'flex',
           alignItems: 'center',
           gap: '8px',
           flexWrap: 'wrap',
        }}>
           {/* Font Size Controls */}
           <span style={{ fontSize: '0.75rem' }}>Size:</span>
           <button
              onClick={() => setLyricsFontSize(Math.max(10, lyricsFontSize - 2))} // Decrease size, min 10px
              style={{
                 padding: '2px 6px', fontSize: '0.7rem', minWidth: 'auto',
                 backgroundColor: '#d1d5db', color: '#374151'
              }}
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag
           >
              A-
           </button>
            <button
              onClick={() => setLyricsFontSize(Math.min(30, lyricsFontSize + 2))} // Increase size, max 30px
              style={{
                 padding: '2px 6px', fontSize: '0.7rem', minWidth: 'auto',
                 backgroundColor: '#d1d5db', color: '#374151'
              }}
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag
           >
              A+
           </button>

           {/* Style Toggles */}
           <span style={{ fontSize: '0.75rem', marginLeft: '8px' }}>Style:</span>
           <button
              onClick={toggleLyricsBold}
              style={{
                 padding: '2px 6px', fontSize: '0.8rem', fontWeight: lyricsIsBold ? 'bold' : 'normal',
                 minWidth: 'auto', backgroundColor: lyricsIsBold ? '#c0c7ce' : '#d1d5db', color: '#374151'
              }}
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag
           >
              B
           </button>
           <button
              onClick={toggleLyricsItalic}
              style={{
                 padding: '2px 6px', fontSize: '0.8rem', fontStyle: lyricsIsItalic ? 'italic' : 'normal',
                 minWidth: 'auto', backgroundColor: lyricsIsItalic ? '#c0c7ce' : '#d1d5db', color: '#374151'
              }}
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag
           >
              <span style={{ fontStyle: 'italic' }}>I</span>
           </button>
           <button
              onClick={toggleLyricsUnderlined}
              style={{
                 padding: '2px 6px', fontSize: '0.8rem', textDecoration: lyricsIsUnderlined ? 'underline' : 'none',
                 minWidth: 'auto', backgroundColor: lyricsIsUnderlined ? '#c0c7ce' : '#d1d5db', color: '#374151'
              }}
              onMouseDown={(e) => e.stopPropagation()} // Prevent drag
           >U</button>
        </div>

        {/* Content Area */}
        <textarea
          value={lyrics}
          onChange={(e) => setLyrics(e.target.value)}
          placeholder="Enter lyrics here..."
          style={{
            flexGrow: 1,
            minHeight: '150px',
            border: 'none',
            padding: '12px',
            fontFamily: 'sans-serif',
            resize: 'none', // Disable default textarea resizing
            outline: 'none',
            fontSize: `${lyricsFontSize}px`, // Apply font size from store
            fontWeight: lyricsIsBold ? 'bold' : 'normal', // Apply bold from store
            fontStyle: lyricsIsItalic ? 'italic' : 'normal', // Apply italic from store
            textDecoration: lyricsIsUnderlined ? 'underline' : 'none', // Apply underline from store
          }}
          // Prevent dragging when interacting with the textarea
          onMouseDown={(e) => e.stopPropagation()}
        />
        {/* Corner resize handle (SE) */}
        <div
          onMouseDown={(e) => {
            e.stopPropagation();
            setOverlayCursor('nwse-resize');
            setIsResizing(true);
            const startX = e.clientX, startY = e.clientY;
            const node = nodeRef.current; if (!node) return;
            const startWidth = node.offsetWidth, startHeight = node.offsetHeight;
            const onMouseMove = (e: MouseEvent) => {
              node.style.width = `${Math.max(100, startWidth + e.clientX - startX)}px`;
              node.style.height = `${Math.max(100, startHeight + e.clientY - startY)}px`;
            };
            const onMouseUp = () => {
              setIsResizing(false);
              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          }}
          style={{
            position: 'absolute', right: '4px', bottom: '4px',
            width: '16px', height: '16px', cursor: 'se-resize', zIndex: 1001
          }}
        />
        {/* Bottom-edge resize handle (vertical) */}
        <div
          onMouseDown={(e) => {
            e.stopPropagation();
            setOverlayCursor('ns-resize');
            setIsResizing(true);
            const startY = e.clientY;
            const node = nodeRef.current; if (!node) return;
            const startHeight = node.offsetHeight;
            const onMouseMove = (e: MouseEvent) => {
              node.style.height = `${Math.max(100, startHeight + e.clientY - startY)}px`;
            };
            const onMouseUp = () => {
              setIsResizing(false);
              setOverlayCursor('move'); // Reset cursor on mouseup
              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          }}
          style={{
            position: 'absolute', left: '50%', bottom: '2px',
            width: '80%', // Make it a bit wider
            height: '12px', // Increase height for easier clicking
            transform: 'translateX(-50%)',
            cursor: 'ns-resize',
            backgroundColor: isResizing ? 'rgba(0, 0, 0, 0.1)' : 'transparent', // Visual feedback when dragging
            zIndex: 1001,
            borderRadius: '4px' // Soften corners
          }}
        >
          {/* Grip lines */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '24px', // Width of grip lines area
            height: '2px', // Height of grip lines
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            {[0, 1, 2].map(i => (
              <div key={i} style={{ width: '6px', height: '2px', backgroundColor: '#aaa', borderRadius: '1px' }} />
            ))}
          </div>
        </div>
      </div>
    </Draggable>
  );
};

export default DraggableLyricsPopup;
