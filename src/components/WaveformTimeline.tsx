import React, { useState, useRef, useEffect } from 'react';
import { useAudioStore } from '../store/useAudioStore';

// Add global type declarations
declare global {
  interface Window {
    updatePlayhead: (time: number) => void;
    // Reference to LoopControls' isHoldingEndRef
    isHoldingEndRef: React.MutableRefObject<boolean> | undefined;
  }
}

interface WaveformTimelineProps {
  onZoomChange?: (zoomLevel: number) => void;
}

const TimelineHandle: React.FC<{
  position: number;
  onDrag: (newPosition: number) => void;
  type: 'start' | 'end';
  time: number;
  onDragEnd?: (time: number) => void;
  disabled?: boolean;
}> = ({ position, onDrag, type, time, onDragEnd, disabled = false }) => {
  // Get formatTime function from the component itself
  const formatTime = (TimelineHandle as any).formatTime || ((t: number) => `${Math.floor(t)}s`);
  const [isDragging, setIsDragging] = useState(false);
  const handleRef = useRef<HTMLDivElement>(null);

  // Handle mouse down to start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return; // Prevent dragging when disabled
    e.stopPropagation();
    setIsDragging(true);
    if (type === 'start') {
      (YouTubeTimeline as any).setIsDraggingStart(true);
    } else {
      (YouTubeTimeline as any).setIsDraggingEnd(true);
    }
  };

  // Handle mouse move for dragging
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !handleRef.current) return;

      // Find the timeline container (grandparent element)
      const parent = handleRef.current.parentElement;
      if (!parent || !parent.parentElement) return;

      const timelineContainer = parent.parentElement;
      const rect = timelineContainer.getBoundingClientRect();
      const scrollLeft = timelineContainer.scrollLeft;

      // Calculate position considering scroll
      const totalWidth = timelineContainer.scrollWidth;
      const clickX = e.clientX - rect.left + scrollLeft;
      const newPosition = Math.max(0, Math.min(1, clickX / totalWidth));

      // Throttle drag updates for better performance
      // This prevents too many YouTube API calls
      requestAnimationFrame(() => {
        onDrag(newPosition);
      });

      // Auto-scroll if dragging near edges
      const edgeThreshold = 50; // pixels from edge to trigger scroll
      if (e.clientX < rect.left + edgeThreshold) {
        // Near left edge, scroll left
        timelineContainer.scrollBy({ left: -15, behavior: 'auto' });
      } else if (e.clientX > rect.right - edgeThreshold) {
        // Near right edge, scroll right
        timelineContainer.scrollBy({ left: 15, behavior: 'auto' });
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      if (type === 'start') {
        (YouTubeTimeline as any).setIsDraggingStart(false);
      } else {
        (YouTubeTimeline as any).setIsDraggingEnd(false);
      }
      // Call onDragEnd if provided
      if (onDragEnd) {
        onDragEnd(time);
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, onDrag]);

  return (
    <div
      ref={handleRef}
      className="timeline-handle"
      onMouseDown={handleMouseDown}
      style={{
        position: 'absolute',
        left: `${position}%`,
        top: 0,
        bottom: 0,
        width: '8px', // Reduced width
        marginLeft: type === 'start' ? '-4px' : '-4px', // Adjusted margin
        backgroundColor: disabled ? '#9ca3af' : '#2563eb', // Gray when disabled
        cursor: disabled ? 'not-allowed' : 'ew-resize',
        zIndex: 15,
        borderRadius: '2px',
        boxShadow: disabled ? '0 0 2px rgba(156, 163, 175, 0.4)' : '0 0 4px rgba(37, 99, 235, 0.6)', // Reduced shadow when disabled
        opacity: disabled ? 0.5 : (isDragging ? 1 : 0.8),
        transition: isDragging ? 'none' : 'opacity 0.2s ease',
        pointerEvents: disabled ? 'none' : 'auto' // Disable pointer events when disabled
      }}
    >
      {/* Handle grip lines for better visibility */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '2px', // Reduced grip width
        height: '16px', // Reduced grip height
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        {[0, 1, 2].map(i => (
          <div
            key={i}
            style={{
              width: '2px',
              height: '3px', // Reduced grip line height
              backgroundColor: disabled ? '#d1d5db' : 'white', // Lighter color when disabled
              borderRadius: '1px'
            }}
          />
        ))}
      </div>

      {/* Label showing time */}
      <div style={{
        position: 'absolute',
        bottom: '-20px',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: disabled ? '#9ca3af' : '#2563eb',
        color: 'white',
        padding: '1px 3px', // Reduced padding
        borderRadius: '2px',
        fontSize: '0.5rem', // Reduced font size
        whiteSpace: 'nowrap',
        opacity: isDragging ? 1 : 0,
        transition: 'opacity 0.2s ease',
        pointerEvents: 'none'
      }}>
        {`${type === 'start' ? 'Start' : 'End'}: ${formatTime(time)}`}
      </div>
    </div>
  );
};

const YouTubeTimeline: React.FC<WaveformTimelineProps> = ({ onZoomChange }) => {
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const timelineRef = useRef<HTMLDivElement>(null);
  // Ref to track if a seek operation is currently in progress
  const isSeekingRef = useRef(false);
  const [isDraggingPlayhead, setIsDraggingPlayhead] = useState(false);
  // Ref to track dragging of loop handles (start or end)
  const isDraggingHandleRef = useRef<'start' | 'end' | null>(null);
  // State to track if we're in "setting end" mode after a YouTube shift+click
  const [isSettingLoopEnd, setIsSettingLoopEnd] = useState(false);
  // State and ref to track if we're creating a new loop by dragging
  const [isCreatingNewLoop, setIsCreatingNewLoop] = useState(false);
  const newLoopStartTimeRef = useRef<number>(0);

  const {
    currentTime,
    duration,
    loopActive,
    loopStart,
    loopEnd,
    setLoopRegion,
    setLoopStart,
    setLoopEnd,
    setCurrentTime,
    isPlaying,
    savedLoops,
    activeLoopId,
    showDisabledLoops,
    setShowDisabledLoops
  } = useAudioStore();

// Format time as MM:SS
const formatTime = (time: number): string => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

// Make formatTime available to TimelineHandle
(TimelineHandle as any).formatTime = formatTime;

// Calculate position percentage based on time
const getPositionPercent = (time: number): number => {
  return duration > 0 ? (time / duration) * 100 : 0;
};

// Get access to the YouTube player instance
const getYouTubePlayer = () => {
  try {
    // First try to find the iframe directly
    const iframes = document.querySelectorAll('iframe');
    for (let i = 0; i < iframes.length; i++) {
      const iframe = iframes[i];
      if (iframe.src.includes('youtube.com')) {
        const id = iframe.id;
        // @ts-ignore - YT might not be defined in the window object
        if (window.YT && window.YT.get && id) {
          // @ts-ignore
          return window.YT.get(id);
        }
      }
    }

    // If that fails, try to access the player through the window object
    // @ts-ignore - playerRef might be defined by the YouTubePlayer component
    if (window.youtubePlayerRef && typeof window.youtubePlayerRef === 'object') {
      // @ts-ignore
      return window.youtubePlayerRef;
    }

    console.warn('Could not find YouTube player by iframe or window reference');
    return null;
  } catch (error) {
    console.error('Error accessing YouTube player:', error);
    return null;
  }
};

// Directly seek the YouTube player to a specific time
const seekYouTubePlayer = (time: number) => {
  try {
    // First update our state immediately
    setCurrentTime(time);

    // Indicate that we are initiating a seek
    isSeekingRef.current = true;
    // Reset the seeking flag after a short delay, in case confirmation fails
    setTimeout(() => {
      isSeekingRef.current = false;
    }, 500); // 500ms timeout

    // Then try to seek the actual YouTube player
    const player = getYouTubePlayer();
    if (player && typeof player.seekTo === 'function') {
      player.seekTo(time, true);

      // Dispatch a seek confirmed event immediately after telling the player to seek
      // The player component will restart its interval upon receiving this.
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('youtube-seek-confirmed', {
          detail: { time }
        }));
      }, 10);

      return true;
    }

    // If direct access fails, use a fallback method
    // This dispatches a custom event that the YouTubePlayer component can listen for
    const seekEvent = new CustomEvent('youtube-seek', { detail: { time } });
    window.dispatchEvent(seekEvent);

    return true;
  } catch (error) {
    console.error('Error seeking YouTube player:', error);
    return false;
  }
};

// Handle drag of loop start handle
const handleStartDrag = (newPosition: number) => {
  const newStart = newPosition * duration;
  // Ensure start is before end (if end is set)
  if (loopEnd > loopStart + 0.1) {
    // We have a valid end, ensure start doesn't go past it
    if (newStart < loopEnd - 0.5) {
      setLoopStart(newStart);
    }
  } else {
    // No valid end set, just update start
    setLoopStart(newStart);
  }
};

// Handle drag of loop end handle
const handleEndDrag = (newPosition: number) => {
  const newEnd = newPosition * duration;
  // Ensure end is after start
  if (newEnd > loopStart + 0.5) {
    setLoopEnd(newEnd);
  }
};

// Handle mouse up after dragging to ensure final position is set
const handleDragEnd = (time: number) => {
  // Always seek when drag ends
  seekYouTubePlayer(time);
};

// We don't need to manually handle looping here anymore
// The YouTubePlayer component already handles looping
// This was causing double-looping issues

// Notify parent when zoom level changes
useEffect(() => {
  if (onZoomChange) {
    onZoomChange(zoomLevel);
  }
}, [zoomLevel, onZoomChange]);

// Keep playhead visible by scrolling the timeline container
useEffect(() => {
  if (!timelineRef.current) return;
}, [isPlaying, zoomLevel]);

// Keep playhead in sync with video playback and handle looping
useEffect(() => {
  // This ensures the timeline is always in sync with the YouTube player
  // We don't need an interval anymore since we're using events

  // Listen for time update events from the YouTube player
  const handleTimeUpdateEvent = (e: CustomEvent) => {
    // Only update the state. The component will re-render based on this state change.
    // *** Ignore time updates if we are currently seeking ***
    if (isSeekingRef.current) {
      return;
    }
    if (e.detail && typeof e.detail.time === 'number') {
      // Update the current time
      setCurrentTime(e.detail.time);
    }
  };

  // Listen for loop events from the YouTube player
  const handleLoopEvent = (e: CustomEvent) => {
    if (e.detail && typeof e.detail.time === 'number') {
      // If a loop happens, we are definitely not seeking anymore
      isSeekingRef.current = false;

      // Force an immediate update of the playhead position
      // Only update the state.
      setCurrentTime(e.detail.time);
    }
  };

  // Listen for seek events from the YouTube player
  // This handles both our custom seek and the confirmation from the player
  const handleSeekEvent = (e: CustomEvent) => {
    if (e.detail && typeof e.detail.time === 'number') {
      // When a seek event (especially confirmation) comes through,
      // reset the seeking flag.
      isSeekingRef.current = false;

      // Force an immediate update of the playhead position
      // Only update the state.
      setCurrentTime(e.detail.time);
    }
  };

  // Listen for shift+click events from the YouTube player
  const handleYouTubeShiftClick = (e: CustomEvent) => {
    if (e.detail && typeof e.detail.time === 'number') {
      const newTime = e.detail.time;

      if (loopActive) {
        // Loop is already active - modify existing loop boundaries
        let newStart = loopStart;
        let newEnd = loopEnd;

        // Determine whether to set start or end (nearest boundary)
        if (Math.abs(newTime - loopStart) < Math.abs(newTime - loopEnd)) {
          newStart = Math.max(0, Math.min(newTime, loopEnd - 0.1)); // Ensure start < end
        } else {
          newEnd = Math.min(duration, Math.max(newTime, loopStart + 0.1)); // Ensure end > start
        }

        setLoopRegion(newStart, newEnd);
        setIsSettingLoopEnd(false); // Reset the setting end mode
      } else {
        // No loop active - create a new loop starting at this time and enter "setting end" mode
        setLoopStart(newTime);
        setIsSettingLoopEnd(true); // Enable "setting end" mode
        // Note: setLoopStart will automatically activate the loop in the store
      }
    }
  };

  // Add event listeners for custom events
  window.addEventListener('youtube-time-update', handleTimeUpdateEvent as EventListener);
  window.addEventListener('youtube-loop', handleLoopEvent as EventListener);
  window.addEventListener('youtube-seek-confirmed', handleSeekEvent as EventListener); // Use same handler for confirmed
  window.addEventListener('youtube-seek', handleSeekEvent as EventListener);
  window.addEventListener('youtube-shift-click', handleYouTubeShiftClick as EventListener);

  // Add a global debug function to update the playhead position
  window.updatePlayhead = (time: number) => {
    setCurrentTime(time);
  };

  return () => {
    // Clean up listeners
    window.removeEventListener('youtube-time-update', handleTimeUpdateEvent as EventListener);
    window.removeEventListener('youtube-loop', handleLoopEvent as EventListener);
    window.removeEventListener('youtube-seek', handleSeekEvent as EventListener);
    window.removeEventListener('youtube-seek-confirmed', handleSeekEvent as EventListener);
    window.removeEventListener('youtube-shift-click', handleYouTubeShiftClick as EventListener);
  };
}, [setCurrentTime]);

// Handle escape key to cancel "setting end" mode
useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isSettingLoopEnd) {
      setIsSettingLoopEnd(false);
    }
  };

  if (isSettingLoopEnd) {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }
}, [isSettingLoopEnd]);

// Generate time markers based on zoom level
const generateTimeMarkers = () => {
  if (duration <= 0) return [];

  const markers = [];
  // Adjust marker density based on zoom level
  const markerCount = 10 * zoomLevel;
  const interval = Math.ceil(duration / markerCount);

  for (let time = 0; time <= duration; time += interval) {
    const percent = (time / duration) * 100;
    const isMainMarker = time % (interval * 2) === 0;

    markers.push(
      <div
        key={time}
        style={{
          position: 'absolute',
          left: `${percent}%`,
          top: 0,
          width: isMainMarker ? '2px' : '1px',
          height: isMainMarker ? '12px' : '8px',
          backgroundColor: isMainMarker ? '#6b7280' : '#9ca3af'
        }}
      />
    );

    // Add time labels for main markers
    if (isMainMarker) {
      markers.push(
        <div
          key={`label-${time}`}
          style={{
            position: 'absolute',
            left: `${percent}%`,
            top: '14px',
            fontSize: '0.6rem',
            color: '#6b7280',
            transform: 'translateX(-50%)',
            whiteSpace: 'nowrap'
          }}
        >
          {formatTime(time)}
        </div>
      );
    }
  }

  return markers;
};

// Handle zoom in/out
const handleZoomIn = () => {
  setZoomLevel(prev => Math.min(prev + 0.5, 5));
};

const handleZoomOut = () => {
  setZoomLevel(prev => Math.max(prev - 0.5, 1));
};

const handleTimelineMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
  if (!timelineRef.current) return;

  const rect = timelineRef.current.getBoundingClientRect();
  const scrollLeft = timelineRef.current.scrollLeft;
  const totalWidth = timelineRef.current.scrollWidth;
  const clickX = e.clientX - rect.left + scrollLeft;
  const newPosition = Math.max(0, Math.min(1, clickX / totalWidth));
  const newTime = newPosition * duration;

  if (e.shiftKey) {
    // Shift+Click: Set loop region or create new loop

    if (isSettingLoopEnd) {
      // We're in "setting end" mode from a previous YouTube shift+click
      const newEnd = Math.min(duration, Math.max(newTime, loopStart + 0.1)); // Ensure end > start
      setLoopEnd(newEnd);
      setIsSettingLoopEnd(false); // Exit setting end mode
      return;
    }

    if (loopActive) {
      // Loop is already active - modify existing loop boundaries
      let newStart = loopStart;
      let newEnd = loopEnd;

      // Determine whether to set start or end (nearest boundary)
      if (Math.abs(newTime - loopStart) < Math.abs(newTime - loopEnd)) {
        newStart = Math.max(0, Math.min(newTime, loopEnd - 0.1)); // Ensure start < end
      } else {
        newEnd = Math.min(duration, Math.max(newTime, loopStart + 0.1)); // Ensure end > start
      }

      setLoopRegion(newStart, newEnd);
      // Begin dragging the handle that was just set
      isDraggingHandleRef.current = (Math.abs(newTime - loopStart) < Math.abs(newTime - loopEnd)) ? 'start' : 'end';
      setIsDraggingPlayhead(true);
    } else {
      // No loop active - create a new loop starting at this time and enable drag-to-create mode
      setLoopStart(newTime);
      newLoopStartTimeRef.current = newTime;
      setIsCreatingNewLoop(true);
      setIsDraggingPlayhead(true);
      // Note: setLoopStart will automatically activate the loop in the store
    }
    setIsSettingLoopEnd(false); // Reset setting end mode
    return;
  } else if (isSettingLoopEnd) {
    // Normal click while in "setting end" mode - set the loop end
    const newEnd = Math.min(duration, Math.max(newTime, loopStart + 0.1)); // Ensure end > start
    setLoopEnd(newEnd);
    setIsSettingLoopEnd(false); // Exit setting end mode
    return;
  } else {
    // Normal Click: Start dragging playhead (seeking)
    setIsDraggingPlayhead(true); // Start dragging state immediately

    // Immediately seek to the clicked position
    setCurrentTime(newTime);
    seekYouTubePlayer(newTime);
  }
};

useEffect(() => {
  // Only add listeners if dragging playhead or handle
  if (!isDraggingPlayhead) return;

  const handleMouseMove = (e: MouseEvent) => {
    if (!timelineRef.current) return;
    e.preventDefault();
    const rect = timelineRef.current.getBoundingClientRect();
    const scrollLeft = timelineRef.current.scrollLeft;
    const totalWidth = timelineRef.current.scrollWidth;
    const clickX = e.clientX - rect.left + scrollLeft;
    const newPosition = Math.max(0, Math.min(1, clickX / totalWidth));
    const newTime = newPosition * duration;
    // If creating a new loop, update both start and end based on drag direction
    if (isCreatingNewLoop) {
      const startTime = newLoopStartTimeRef.current;
      if (newTime >= startTime) {
        // Dragging right - keep start, update end
        setLoopRegion(startTime, newTime);
      } else {
        // Dragging left - update start, keep original position as end
        setLoopRegion(newTime, startTime);
      }
    } else if (isDraggingHandleRef.current === 'start') {
      handleStartDrag(newPosition);
    } else if (isDraggingHandleRef.current === 'end') {
      handleEndDrag(newPosition);
    } else {
      setCurrentTime(newTime);
      seekYouTubePlayer(newTime);
    }
  };

  const handleMouseUp = () => {
    setIsDraggingPlayhead(false);
    // Stop any handle dragging as well
    isDraggingHandleRef.current = null;
    // Reset new loop creation state
    setIsCreatingNewLoop(false);
    requestAnimationFrame(() => {});
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);

  return () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };
}, [isDraggingPlayhead]);

return (
  <div style={{ width: '100%', marginBottom: '1rem' }}>
    <div style={{ marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <span>
        Timeline ({formatTime(currentTime)} / {formatTime(duration)})
        {isSettingLoopEnd && (
          <span style={{
            marginLeft: '1rem',
            color: '#f59e0b',
            fontWeight: 'bold',
            fontSize: '0.75rem'
          }}>
            Click to set loop end (ESC to cancel)
          </span>
        )}
      </span>
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.75rem' }}>
          <input
            type="checkbox"
            checked={showDisabledLoops}
            onChange={(e) => setShowDisabledLoops(e.target.checked)}
            style={{ width: '0.875rem', height: '0.875rem' }}
          />
          <span>Show disabled loops</span>
        </label>
        <div>
          <button onClick={handleZoomIn} disabled={zoomLevel >= 5} style={{ marginRight: '0.5rem' }}>Zoom In</button>
          <button onClick={handleZoomOut} disabled={zoomLevel <= 1}>Zoom Out</button>
        </div>
      </div>
    </div>
    <div
      ref={timelineRef}
      onMouseDown={handleTimelineMouseDown}
      style={{
        position: 'relative',
        height: '5rem',
        backgroundColor: isSettingLoopEnd ? '#fef3c7' : '#e5e7eb', // Light yellow when setting end
        borderRadius: '0.5rem',
        overflowX: 'auto',
        overflowY: 'hidden',
        // Disable pointer events on the timeline background when dragging
        // either the playhead or the loop handles (internal or external "set current" button)
        pointerEvents: (isDraggingPlayhead || isDraggingHandleRef.current !== null || window.isHoldingEndRef?.current) ? 'none' : 'auto',
        cursor: isSettingLoopEnd ? 'crosshair' : (isDraggingPlayhead || isDraggingHandleRef.current !== null || window.isHoldingEndRef?.current) ? 'grabbing' : 'pointer',
        userSelect: 'none', // Prevent text selection
        border: isSettingLoopEnd ? '2px solid #f59e0b' : 'none', // Orange border when setting end
        boxSizing: 'border-box'
      }}
    >
      {/* Inner timeline that scales with zoom */}
      <div style={{
        position: 'relative',
        width: `${100 * zoomLevel}%`,
        height: '100%',
        minWidth: '100%'
      }}>

      {/* Time markers */}
      {generateTimeMarkers()}

      {/* Timeline track */}
      <div style={{
        position: 'absolute',
        left: 0,
        right: 0,
        top: '50%',
        height: '4px',
        backgroundColor: '#9ca3af',
        transform: 'translateY(-50%)'
      }} />

      {/* Current position indicator (playhead) - always on top */}
      <div
        id="timeline-playhead"
        style={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          width: '2px',
          backgroundColor: '#ef4444',
          left: `${getPositionPercent(currentTime)}%`,
          zIndex: 100, // Very high z-index to stay on top
          boxShadow: '0 0 8px rgba(239, 68, 68, 1), 0 0 4px rgba(0, 0, 0, 0.5)',
          pointerEvents: 'none', // Allow clicking through the playhead
          transition: 'none', // Remove transition for direct control via state updates
          willChange: 'left', // Optimize for animation performance
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
      />

      {/* Current active loop region - show visual feedback even when not actively looping */}
      {(loopActive || loopStart > 0 || loopEnd > loopStart + 0.1) && (
        <>
          {/* Loop region bar - only show if we have a valid range */}
          {loopEnd > loopStart + 0.1 && (
            <div style={{
              position: 'absolute',
              left: `${getPositionPercent(loopStart)}%`,
              width: `${getPositionPercent(loopEnd) - getPositionPercent(loopStart)}%`,
              top: '50%',
              height: '8px',
              backgroundColor: loopActive ? '#2563eb' : '#9ca3af', // Blue when active, more gray when inactive
              opacity: loopActive ? 1 : 0.4, // More transparent when inactive
              transform: 'translateY(-50%)',
              zIndex: 5,
              pointerEvents: 'none' // Allow clicking through the region
            }} />
          )}

          {/* Start marker line - show when only start is set */}
          {loopStart > 0 && loopEnd <= loopStart + 0.1 && (
            <div style={{
              position: 'absolute',
              left: `${getPositionPercent(loopStart)}%`,
              top: '25%',
              bottom: '25%',
              width: '3px',
              backgroundColor: loopActive ? '#f59e0b' : '#9ca3af', // Orange when active, gray when inactive
              opacity: loopActive ? 0.8 : 0.4, // More transparent when inactive
              zIndex: 5,
              pointerEvents: 'none',
              borderRadius: '1px'
            }} />
          )}

          {/* Start handle - always show if loopStart is set */}
          {loopStart > 0 && (
            <TimelineHandle
              position={getPositionPercent(loopStart)}
              onDrag={handleStartDrag}
              type="start"
              time={loopStart}
              onDragEnd={handleDragEnd}
              disabled={!loopActive}
            />
          )}

          {/* End handle - only show if we have a valid end */}
          {loopEnd > loopStart + 0.1 && (
            <TimelineHandle
              position={getPositionPercent(loopEnd)}
              onDrag={handleEndDrag}
              type="end"
              time={loopEnd}
              onDragEnd={handleDragEnd}
              disabled={!loopActive}
            />
          )}
        </>
      )}

      {/* All saved loops - show when showDisabledLoops is enabled */}
      {showDisabledLoops && savedLoops.map(loop => {
        // Skip the currently active loop as it's already rendered above
        if (loop.id === activeLoopId) return null;

        // All saved loops shown here are disabled (not the active one)

        return (
          <div key={loop.id}>
            {/* Loop region bar - only show if we have a valid range */}
            {loop.end !== null && loop.end > loop.start + 0.1 && (
              <div style={{
                position: 'absolute',
                left: `${getPositionPercent(loop.start)}%`,
                width: `${getPositionPercent(loop.end) - getPositionPercent(loop.start)}%`,
                top: '50%',
                height: '6px', // Slightly smaller for disabled loops
                backgroundColor: '#9ca3af', // Gray for disabled loops
                opacity: 0.3, // More transparent for disabled loops
                transform: 'translateY(-50%)',
                zIndex: 3, // Lower z-index than active loop
                pointerEvents: 'none'
              }} />
            )}

            {/* Start marker line - show when only start is set */}
            {loop.start > 0 && (loop.end === null || loop.end <= loop.start + 0.1) && (
              <div style={{
                position: 'absolute',
                left: `${getPositionPercent(loop.start)}%`,
                top: '25%',
                bottom: '25%',
                width: '2px', // Slightly smaller for disabled loops
                backgroundColor: '#9ca3af', // Gray for disabled loops
                opacity: 0.3, // More transparent for disabled loops
                zIndex: 3,
                pointerEvents: 'none',
                borderRadius: '1px'
              }} />
            )}

            {/* Start handle for disabled loops */}
            {loop.start > 0 && (
              <TimelineHandle
                position={getPositionPercent(loop.start)}
                onDrag={() => {}} // No drag functionality for disabled loops
                type="start"
                time={loop.start}
                disabled={true}
              />
            )}

            {/* End handle for disabled loops */}
            {loop.end !== null && loop.end > loop.start + 0.1 && (
              <TimelineHandle
                position={getPositionPercent(loop.end)}
                onDrag={() => {}} // No drag functionality for disabled loops
                type="end"
                time={loop.end}
                disabled={true}
              />
            )}
          </div>
        );
      })}
      </div>
    </div>

    {/* Time markers */}
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      fontSize: '0.75rem',
      color: '#6b7280',
      marginTop: '0.25rem',
      padding: '0 0.25rem'
    }}>
      <div>0:00</div>
      <div>{formatTime(duration)}</div>
    </div>
  </div>
);
};

// Export as AudioTimeline for backward compatibility
export default YouTubeTimeline;
