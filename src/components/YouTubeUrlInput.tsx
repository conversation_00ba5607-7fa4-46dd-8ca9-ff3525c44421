import { useState } from 'react';
import { useAudioStore } from '../store/useAudioStore';

const YouTubeUrlInput: React.FC = () => {
  const [inputUrl, setInputUrl] = useState('');
  const { setYoutubeUrl } = useAudioStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputUrl.trim()) {
      setYoutubeUrl(inputUrl);
    }
  };

  return (
    <form onSubmit={handleSubmit} style={{ width: '100%', maxWidth: '28rem', margin: '0 auto', marginBottom: '1.5rem' }}>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
        <input
          type="text"
          value={inputUrl}
          onChange={(e) => setInputUrl(e.target.value)}
          placeholder="Enter YouTube URL"
          style={{ flex: 1, padding: '0.5rem 1rem', border: '1px solid #d1d5db', borderRadius: '0.375rem' }}
        />
        <button
          type="submit"
          style={{ padding: '0.5rem 1rem', backgroundColor: '#2563eb', color: 'white', borderRadius: '0.375rem', border: 'none', cursor: 'pointer' }}
        >
          Load Video
        </button>
      </div>
    </form>
  );
};

export default YouTubeUrlInput;
