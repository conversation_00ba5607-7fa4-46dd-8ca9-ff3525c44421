import { useState, useEffect, useCallback } from 'react';
import { useAudioStore } from '../store/useAudioStore';
import YouTubeTimeline from './WaveformTimeline';
import MidiTimelineVisualization from './MidiTimelineVisualization';
import Tooltip from './Tooltip';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { MidiNote } from '../types/midi';

interface TransportControlsProps {
  midiNotes?: MidiNote[];
  midiEditorState?: {
    bpm: number;
    referenceStartTime: number | null;
    selectedKey: string;
    selectedMode: 'major' | 'minor';
    zoom: number;
  };
}

const TransportControls: React.FC<TransportControlsProps> = ({
  midiNotes = [],
  midiEditorState
}) => {
  const [timelineZoomLevel, setTimelineZoomLevel] = useState(1);

  const {
    isPlaying,
    setIsPlaying,
    currentTime,
    duration,
    setCurrentTime
  } = useAudioStore();
  const {
    loopStart,
    loopEnd,
    loopActive,
    savedLoops,
    activeLoopId,
    addSavedLoop,
    updateSavedLoop,
    removeSavedLoop,
    setActiveLoop,
    setLoopActive
  } = useAudioStore();



  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getLoopTooltipContent = (loop: { start: number; end: number | null }) => {
    if (loop.end === null) {
      return `Start: ${formatTime(loop.start)}`;
    }
    return `${formatTime(loop.start)} - ${formatTime(loop.end)}`;
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleStop = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handleSaveLoop = () => {
    // Determine if this should be a partial loop (no valid end time set)
    const hasValidEnd = loopEnd > loopStart + 0.1; // Need at least 0.1s gap
    const isPartialLoop = !hasValidEnd;

    // Check for duplicates based on loop type
    const dup = savedLoops.find(l => {
      const startMatch = Math.abs(l.start - loopStart) < 0.01;
      if (isPartialLoop) {
        return startMatch && l.end === null;
      } else {
        return startMatch && l.end !== null && Math.abs(l.end - loopEnd) < 0.01;
      }
    });

    if (dup) {
      setActiveLoop(dup.id);
      if (isPartialLoop) {
        toast.info(`Partial loop "${dup.name}" already exists starting at ${formatTime(loopStart)}.`);
      } else {
        toast.info(`Loop "${dup.name}" already exists from ${formatTime(loopStart)} to ${formatTime(loopEnd)}.`);
      }
      return;
    }

    // Generate appropriate name based on loop type
    if (isPartialLoop) {
      const nextIndex = savedLoops.reduce((max, l) => {
        const m = /^Partial Loop (\d+)$/.exec(l.name);
        return m ? Math.max(max, Number(m[1])) : max;
      }, 0) + 1;
      addSavedLoop(`Partial Loop ${nextIndex}`, loopStart, null);
    } else {
      const nextIndex = savedLoops.reduce((max, l) => {
        const m = /^Loop (\d+)$/.exec(l.name);
        return m ? Math.max(max, Number(m[1])) : max;
      }, 0) + 1;
      addSavedLoop(`Loop ${nextIndex}`, loopStart, loopEnd);
    }
  };

  const handleUpdateLoop = () => {
    if (!activeLoopId) return;
    updateSavedLoop(activeLoopId, loopStart, loopEnd);
  };

  const handleDeleteLoop = () => {
    if (!activeLoopId) return;
    removeSavedLoop(activeLoopId);
  };

  const handleExitLoop = () => {
    setLoopActive(false);
    // Note: activeLoopId is automatically cleared by setLoopActive in the store
  };



  // bind 'L' key to save loop, 'S' to update, 'Escape' to exit loop
  useEffect(() => {
    const onKey = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'l') {
        handleSaveLoop();
      } else if (e.key.toLowerCase() === 's' && activeLoopId) {
        handleUpdateLoop();
      } else if (e.key === 'Escape' && loopActive) {
        handleExitLoop();
      }
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [activeLoopId, savedLoops, loopStart, loopEnd, loopActive]);

  // Note: Timeline clickability is managed internally by WaveformTimeline based on dragging state.

  return (
    <div style={{ width: '100%', maxWidth: '48rem', margin: '0 auto', padding: '1rem', backgroundColor: '#f3f4f6', borderRadius: '0.5rem' }}>
      {/* YouTube Timeline */}
      <div style={{ marginBottom: '1rem' }}>
        <YouTubeTimeline onZoomChange={setTimelineZoomLevel} />
        {/* MIDI Notes Visualization */}
        <MidiTimelineVisualization
          notes={midiNotes}
          bpm={midiEditorState?.bpm || 120}
          zoomLevel={midiEditorState?.zoom || 1}
          height={180}
          referenceStartTime={midiEditorState?.referenceStartTime || null}
          selectedKey={midiEditorState?.selectedKey || 'C'}
          selectedMode={midiEditorState?.selectedMode || 'major'}
        />
      </div>

      {/* Saved loops below timeline */}
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem', marginBottom: '1rem' }}>

        {savedLoops.map(loop => (
          <Tooltip key={loop.id} content={getLoopTooltipContent(loop)}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              padding: '0.25rem',
              backgroundColor: loop.id === activeLoopId ? '#2563eb' : (loop.end === null ? '#f59e0b' : '#e5e7eb'),
              borderRadius: '0.25rem',
              border: loop.end === null ? '2px dashed #d97706' : 'none'
            }}>
              <span onClick={() => setActiveLoop(loop.id)} style={{
                cursor: 'pointer',
                color: loop.id === activeLoopId ? 'white' : (loop.end === null ? 'white' : '#374151'),
                fontSize: '0.75rem'
              }}>
                {loop.name}{loop.end === null ? ' (start only)' : ''}
              </span>
              <button onClick={() => removeSavedLoop(loop.id)} style={{
                marginLeft: '0.25rem',
                background: 'none',
                border: 'none',
                color: loop.id === activeLoopId ? 'white' : (loop.end === null ? 'white' : '#6b7280'),
                cursor: 'pointer',
                fontSize: '0.75rem',
                padding: '0.25rem 0.0625rem',
                lineHeight: '1',
                width: 'auto',
                height: 'auto',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                ×
              </button>
            </div>
          </Tooltip>
        ))}
      </div>

      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
        <div style={{ display: 'flex', gap: '0.25rem' }}>
          <button
            onClick={handlePlayPause}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '0.5rem',
              backgroundColor: '#2563eb',
              color: 'white',
              borderRadius: '9999px',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            {isPlaying ? (
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '1.5rem', width: '1.5rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '1.5rem', width: '1.5rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </button>
          <button
            onClick={handleStop}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '0.5rem',
              backgroundColor: '#4b5563',
              color: 'white',
              borderRadius: '9999px',
              border: 'none',
              cursor: 'pointer'
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" style={{ height: '1.5rem', width: '1.5rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
            </svg>
          </button>
          <button onClick={handleSaveLoop} style={{
            padding: '0.5rem',
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            cursor: 'pointer'
          }}>
            Save Loop
          </button>
          {activeLoopId && (
            <>
              <button onClick={handleUpdateLoop} style={{
                padding: '0.5rem',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                cursor: 'pointer'
              }}>
                Update Loop
              </button>
              <button onClick={handleDeleteLoop} style={{
                padding: '0.5rem',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                cursor: 'pointer'
              }}>
                Delete Loop
              </button>
            </>
          )}
          {loopActive && (
            <button onClick={handleExitLoop} style={{
              padding: '0.5rem',
              backgroundColor: '#f59e0b',
              color: 'white',
              border: 'none',
              cursor: 'pointer'
            }}>
              Exit Loop
            </button>
          )}
        </div>

        <div style={{ fontSize: '0.875rem', fontFamily: 'monospace' }}>
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      </div>


    </div>
  );
};

export default TransportControls;
