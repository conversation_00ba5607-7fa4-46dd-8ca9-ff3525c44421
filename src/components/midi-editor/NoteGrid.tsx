import React from 'react';
import './NoteGrid.css';
import { MidiNote } from '../../types/midi';
import MidiNoteDisplay from './MidiNoteDisplay';

interface NoteGridProps {
  octaveCount?: number;
  beatsPerMeasure?: number;
  measuresCount?: number;
  notes?: MidiNote[];
  pixelsPerBeat?: number;
  pixelsPerPitch?: number;
  lowestDisplayPitch?: number; // e.g., MIDI note number for C3 if startOctave is 3
  bpm?: number;
  referenceStartTime?: number | null;
  currentPlayheadTime?: number | null;
  // TODO: Add props for zoom levels, etc.
}

const NoteGrid: React.FC<NoteGridProps> = ({
  octaveCount = 3,
  beatsPerMeasure = 4,
  measuresCount = 8, // Show 8 measures by default
  notes = [], // Default to empty array of notes
  pixelsPerBeat = 20, // Default: 20px per beat
  pixelsPerPitch = 20, // Default: 20px per pitch step (semitone)
  lowestDisplayPitch = 36, // Default: C2 (MIDI note number for the bottom line of the grid if starting at octave 2)
  bpm = 120, // Default BPM if not provided
  referenceStartTime = null, // Default reference start time
  currentPlayheadTime = null,
}) => {
  const notesInOctave = 12;
  const totalPitches = octaveCount * notesInOctave;

  // Create horizontal lines for pitches
  const pitchLines = [];
  for (let i = 0; i <= totalPitches; i++) {
    // Highlight C notes differently or octave lines
    const isOctaveLine = i % notesInOctave === 0;
    // TODO: A more robust way to identify C notes based on startOctave and note sequence
    const lineClass = isOctaveLine ? 'pitch-line octave-line' : 'pitch-line';
    pitchLines.push(<div key={`p-${i}`} className={lineClass} />);
  }

  // Create vertical lines for time (beats and measures)
  const timeLines = [];
  const totalBeats = measuresCount * beatsPerMeasure;
  for (let i = 0; i <= totalBeats; i++) {
    const isMeasureLine = i % beatsPerMeasure === 0;
    const lineClass = isMeasureLine ? 'time-line measure-line' : 'time-line beat-line';
    timeLines.push(<div key={`t-${i}`} className={lineClass} />);
  }

  return (
    <div className="note-grid-container">
      <div className="pitch-lines-wrapper">
        {pitchLines}
      </div>
      <div className="time-lines-wrapper">
        {timeLines}
      </div>
      <div className="notes-area">
        {notes.map(note => (
          <MidiNoteDisplay
            key={note.id}
            note={note}
            pixelsPerBeat={pixelsPerBeat}
            pixelsPerPitch={pixelsPerPitch}
            lowestPitch={lowestDisplayPitch}
            bpm={bpm}
            referenceStartTime={referenceStartTime}
          />
        ))}
        {notes.length === 0 && 
          <p style={{color: '#555', textAlign: 'center', paddingTop: '20px'}}>Notes will appear here</p>
        }
      </div>
      {typeof currentPlayheadTime === 'number' && 
       typeof referenceStartTime === 'number' && 
       bpm > 0 && 
       pixelsPerBeat > 0 &&
       (() => {
         const elapsedSecondsSinceReference = currentPlayheadTime - referenceStartTime;
         if (elapsedSecondsSinceReference < 0) return null; // Don't show playhead before reference time

         const beatsPerSecond = bpm / 60;
         const elapsedBeats = elapsedSecondsSinceReference * beatsPerSecond;
         const playheadXPosition = elapsedBeats * pixelsPerBeat;

         // Ensure playhead is within the grid boundaries (optional, based on desired behavior)
         // const totalGridWidth = totalBeats * pixelsPerBeat;
         // if (playheadXPosition < 0 || playheadXPosition > totalGridWidth) return null;

         return (
           <div 
             className="playhead"
             style={{
               left: `${playheadXPosition}px`,
               height: `${totalPitches * pixelsPerPitch}px` // Make it full height of the note area
             }}
           />
         );
       })()}
    </div>
  );
};

export default NoteGrid;
