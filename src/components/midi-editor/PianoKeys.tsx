import React from 'react';
import './PianoKeys.css';

interface PianoKeyProps {
  isBlackKey: boolean;
  noteName: string; // e.g., C4, F#5
  // onClick?: (noteName: string) => void; // Optional click handler
}

const PianoKey: React.FC<PianoKeyProps> = ({ isBlackKey, noteName }) => {
  let keyClass = isBlackKey ? 'piano-key black-key' : 'piano-key white-key';
  if (!isBlackKey && noteName.startsWith('C')) {
    keyClass += ' highlight-c'; // Add a special class for C notes
  }
  return (
    <div className={keyClass} title={noteName}>
      {/* Optionally display note name, e.g., only for C notes */}
      {noteName.startsWith('C') && <span style={{fontSize: '8px', marginLeft: '2px'}}>{noteName}</span>}
    </div>
  );
};

interface PianoKeysProps {
  startOctave?: number;
  octaveCount?: number;
}

const PianoKeys: React.FC<PianoKeysProps> = ({ startOctave = 3, octaveCount = 2 }) => {
  const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  const keys = [];

  for (let o = 0; o < octaveCount; o++) {
    const currentOctave = startOctave + o;
    for (let i = 0; i < notes.length; i++) {
      const noteName = notes[i] + currentOctave;
      const isBlackKey = notes[i].includes('#');
      keys.push(<PianoKey key={noteName} noteName={noteName} isBlackKey={isBlackKey} />);
    }
  }

  return <div className="piano-keys-container">{keys}</div>;
};

export default PianoKeys;
