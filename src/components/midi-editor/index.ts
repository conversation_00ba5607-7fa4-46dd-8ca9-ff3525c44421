export { default as MidiEditor } from './MidiEditor';
export * from './MidiEditor'; // If you plan to export types or other named exports from MidiEditor.tsx

export { default as PianoKeys } from './PianoKeys';
export * from './PianoKeys'; // If you plan to export types or other named exports from PianoKeys.tsx

export { default as NoteGrid } from './NoteGrid';
export * from './NoteGrid';

export { default as MidiNoteDisplay } from './MidiNoteDisplay';
export * from './MidiNoteDisplay';

// BandLab-style MIDI Editor components
export { default as BandLabMidiEditor } from './BandLabMidiEditor';
export * from './BandLabMidiEditor';

export { default as BandLabMidiHeader } from './BandLabMidiHeader';
export * from './BandLabMidiHeader';

export { default as BandLabPianoKeys } from './BandLabPianoKeys';
export * from './BandLabPianoKeys';

export { default as BandLabNoteGrid } from './BandLabNoteGrid';
export * from './BandLabNoteGrid';

export { default as BandLabMidiNote } from './BandLabMidiNote';
export * from './BandLabMidiNote';
