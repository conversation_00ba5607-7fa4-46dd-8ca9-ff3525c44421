.midi-note-display {
  position: absolute;
  box-sizing: border-box;
  background-color: #3a7afe; /* A nice blue, can be dynamic */
  border: 1px solid #2a62d0;
  border-radius: 3px;
  cursor: grab; /* Indicates it can be moved */
  overflow: hidden;
  color: white;
  font-size: 0.8em;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none; /* Prevent text selection when dragging */
  transition: background-color 0.1s ease; /* For hover effects */
}

.midi-note-display:hover {
  background-color: #4b8aff;
  border-color: #3a7afe;
}

.midi-note-display.is-selected {
  /* Styles for a selected note, e.g., different border or background */
  border-color: #ffcc00; /* Example: Gold border for selection */
  box-shadow: 0 0 5px #ffcc00;
}

.note-info {
  pointer-events: none; /* Ensure text doesn't interfere with drag */
}
