import React from 'react';
import { MidiNote } from '../../types/midi';
import './MidiNoteDisplay.css';

interface MidiNoteDisplayProps {
  note: MidiNote;
  pixelsPerBeat: number;    // To calculate width
  pixelsPerPitch: number;   // To calculate vertical position and height
  lowestPitch: number;      // To calculate vertical offset from the top
  bpm?: number;
  referenceStartTime?: number | null;
  // onClick?: (noteId: string | number) => void;
  // onDragStart?: (noteId: string | number, event: React.MouseEvent) => void;
}

const MidiNoteDisplay: React.FC<MidiNoteDisplayProps> = ({
  note,
  pixelsPerBeat,
  pixelsPerPitch,
  lowestPitch,
  bpm,
  referenceStartTime,
}) => {
  // Log bpm and referenceStartTime to check if they are passed correctly
  // console.log(`MidiNoteDisplay for note ${note.id}: BPM = ${bpm}, RefTime = ${referenceStartTime}`);
  const style: React.CSSProperties = {
    left: `${note.startTime * pixelsPerBeat}px`,
    bottom: `${(note.pitch - lowestPitch) * pixelsPerPitch}px`, // 'bottom' because pitch increases upwards
    width: `${note.duration * pixelsPerBeat}px`,
    height: `${pixelsPerPitch}px`,
    // backgroundColor: `hsl(${note.pitch * 10 % 360}, 70%, 50%)`, // Example: Color based on pitch
  };

  return (
    <div
      className="midi-note-display"
      style={style}
      title={`Pitch: ${note.pitch}, Start: ${note.startTime}, Duration: ${note.duration}`}
    >
      {/* Optionally display note info inside the note block */}
      {/* <span className="note-info">{note.pitch}</span> */}
    </div>
  );
};

export default MidiNoteDisplay;
