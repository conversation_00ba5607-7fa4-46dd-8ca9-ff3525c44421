import React, { useState, useRef, useCallback } from 'react';
import './BandLabMidiNote.css';
import { MidiNote } from '../../types/midi';
import { MidiEditorState } from './BandLabMidiEditor';
import { audioSynth } from '../../utils/audioSynth';

interface BandLabMidiNoteProps {
  note: MidiNote;
  isSelected: boolean;
  pixelsPerBeat: number;
  pixelsPerSemitone: number;
  editorState: MidiEditorState;
  position: { x: number; y: number };
  onUpdate: (updates: Partial<MidiNote>) => void;
  onDelete: () => void;
  onSelect: (selected: boolean, multiSelect: boolean) => void;
}

const BandLabMidiNote: React.FC<BandLabMidiNoteProps> = ({
  note,
  isSelected,
  pixelsPerBeat,
  pixelsPerSemitone,
  editorState,
  position,
  onUpdate,
  onDelete,
  onSelect
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [isVelocityDragging, setIsVelocityDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number; noteStart: number; notePitch: number; initialDuration: number; initialVelocity?: number } | null>(null);
  const [showSnapPreview, setShowSnapPreview] = useState(false);
  const [lastClickTime, setLastClickTime] = useState(0);
  const [preventDoubleClick, setPreventDoubleClick] = useState(false);

  // Use refs for immediate state tracking (not subject to React batching)
  const hasDraggedSinceClickRef = useRef(false);
  const initialClickPositionRef = useRef<{ x: number; y: number } | null>(null);
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const noteRef = useRef<HTMLDivElement>(null);

  const noteWidth = note.duration * pixelsPerBeat;
  const noteHeight = pixelsPerSemitone;

  // Calculate velocity-based opacity and color
  const velocity = note.velocity || 80;
  const velocityRatio = velocity / 127;
  const opacity = 0.6 + (velocityRatio * 0.4); // Range from 0.6 to 1.0

  // Check if this is a currently recording note (only show red while actively recording)
  const isActivelyRecording = typeof note.id === 'string' && note.id.startsWith('rec_') && editorState.isRecording;



  // Get note color based on velocity in velocity mode
  const getNoteColor = () => {
    if (isActivelyRecording) {
      // Only show red color while actively recording
      return isSelected ? '#ff6666' : '#ff4444';
    }
    if (editorState.mode === 'velocity') {
      const hue = velocityRatio * 120; // From red (0) to green (120)
      return `hsl(${hue}, 70%, 50%)`;
    }
    return isSelected ? '#00E5A0' : '#00C37D';
  };

  // Handle mouse down for dragging/selecting
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Track click time and position for double-click detection using refs for immediate access
    const currentTime = Date.now();
    setLastClickTime(currentTime);
    initialClickPositionRef.current = { x: e.clientX, y: e.clientY };
    hasDraggedSinceClickRef.current = false;

    // Clear any existing timeout
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
    }

    // Play the note sound when clicked (for any mode)
    audioSynth.playNote(note.pitch, 0.3, note.velocity || 80);

    if (editorState.mode === 'select') {
      // Handle selection logic
      const isMultiSelect = e.ctrlKey || e.metaKey;

      if (isMultiSelect) {
        // Toggle selection when holding Ctrl/Cmd
        onSelect(!isSelected, true);
      } else {
        // Single click without modifier - select only this note
        onSelect(true, false);
      }

      setIsDragging(true);
      setDragStart({
        x: e.clientX,
        y: e.clientY,
        noteStart: note.startTime,
        notePitch: note.pitch,
        initialDuration: note.duration
      });
    } else if (editorState.mode === 'velocity') {
      // Start velocity dragging for slider-like behavior
      setIsVelocityDragging(true);
      setPreventDoubleClick(true);
      setDragStart({
        x: e.clientX,
        y: e.clientY,
        noteStart: note.startTime,
        notePitch: note.pitch,
        initialDuration: note.duration,
        initialVelocity: note.velocity || 80
      });

      // Don't change velocity immediately - wait for actual dragging movement
      // Just play the note with current velocity for feedback
      audioSynth.playNote(note.pitch, 0.2, note.velocity || 80);
    }
  }, [editorState.mode, isSelected, note.startTime, note.pitch, note.velocity, noteHeight, onSelect, onUpdate]);

  // Utility function to quantize time values (consistent with grid)
  const quantizeTime = useCallback((time: number) => {
    // Handle special case for "free" mode
    if (editorState.quantizeValue === 'free') {
      return time; // No quantization in free mode
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return time; // Invalid format, no quantization
    }

    return Math.round(time / beatSubdivision) * beatSubdivision;
  }, [editorState.quantizeValue]);

  // Utility function to quantize duration values (for note stretching)
  const quantizeDuration = useCallback((duration: number) => {
    // Handle special case for "free" mode
    if (editorState.quantizeValue === 'free' || editorState.freeMovement) {
      return Math.max(0.0625, duration); // Minimum 1/16 note for free movement
    }

    // Parse fraction like "1/16" or whole number like "1"
    const quantizeFraction = editorState.quantizeValue.split('/');
    let beatSubdivision: number;

    if (quantizeFraction.length === 1) {
      // Whole number (like "1" for whole notes)
      const wholeNotes = parseInt(quantizeFraction[0]);
      beatSubdivision = wholeNotes * 4; // 1 whole note = 4 quarter notes
    } else if (quantizeFraction.length === 2) {
      // Fraction (like "1/16")
      const numerator = parseInt(quantizeFraction[0]);
      const denominator = parseInt(quantizeFraction[1]);
      beatSubdivision = (numerator / denominator) * 4; // Convert to quarter note beats
    } else {
      return Math.max(0.25, duration); // Default minimum quarter note
    }

    // Ensure minimum duration is one grid subdivision
    const minDuration = beatSubdivision;
    const quantizedDuration = Math.round(duration / beatSubdivision) * beatSubdivision;
    return Math.max(minDuration, quantizedDuration);
  }, [editorState.quantizeValue, editorState.freeMovement]);

  // Handle mouse move for dragging
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !dragStart || editorState.mode !== 'select') return;

    // Check if user has moved mouse significantly since initial click using refs
    if (initialClickPositionRef.current && !hasDraggedSinceClickRef.current) {
      const deltaFromInitial = Math.abs(e.clientX - initialClickPositionRef.current.x) + Math.abs(e.clientY - initialClickPositionRef.current.y);
      if (deltaFromInitial > 3) { // Lower threshold for more sensitive detection
        hasDraggedSinceClickRef.current = true;
        console.log('Drag detected - double-click deletion will be prevented');
      }
    }

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    const beatDelta = deltaX / pixelsPerBeat;
    const semitoneDelta = -Math.round(deltaY / pixelsPerSemitone); // Negative because Y increases downward

    const newStartTime = Math.max(0, dragStart.noteStart + beatDelta);
    const newPitch = Math.max(0, Math.min(127, dragStart.notePitch + semitoneDelta));

    // Play sound when pitch changes during dragging
    if (newPitch !== note.pitch) {
      audioSynth.playNote(newPitch, 0.2, note.velocity || 80);
    }

    // Apply quantization based on freeMovement setting
    const finalStartTime = editorState.freeMovement ? newStartTime : quantizeTime(newStartTime);

    onUpdate({
      startTime: finalStartTime,
      pitch: newPitch
    });
  }, [isDragging, dragStart, editorState.mode, editorState.freeMovement, pixelsPerBeat, pixelsPerSemitone, onUpdate, quantizeTime, note.pitch, note.velocity]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
    setIsVelocityDragging(false);
    setShowSnapPreview(false);
    setDragStart(null);

    // Reset double-click prevention after a short delay
    setTimeout(() => {
      setPreventDoubleClick(false);
    }, 100);

    // Reset drag tracking after a longer delay to allow double-click detection to work
    dragTimeoutRef.current = setTimeout(() => {
      initialClickPositionRef.current = null;
      hasDraggedSinceClickRef.current = false;
    }, 600); // Longer delay to ensure double-click window is preserved
  }, []);

  // Handle resize start
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Prevent double-click deletion during resize
    setPreventDoubleClick(true);

    setIsResizing(true);
    setShowSnapPreview(!editorState.freeMovement && editorState.quantizeValue !== 'free');
    setDragStart({
      x: e.clientX,
      y: e.clientY,
      noteStart: note.startTime,
      notePitch: note.pitch,
      initialDuration: note.duration
    });
  }, [note.startTime, note.pitch, note.duration, editorState.freeMovement, editorState.quantizeValue]);

  // Handle resize move
  const handleResizeMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !dragStart) return;

    // Mark that we've moved during resize to prevent double-click deletion using refs
    if (initialClickPositionRef.current && !hasDraggedSinceClickRef.current) {
      const deltaFromInitial = Math.abs(e.clientX - initialClickPositionRef.current.x) + Math.abs(e.clientY - initialClickPositionRef.current.y);
      if (deltaFromInitial > 3) { // Lower threshold for more sensitive detection
        hasDraggedSinceClickRef.current = true;
        console.log('Resize drag detected - double-click deletion will be prevented');
      }
    }

    const deltaX = e.clientX - dragStart.x;
    const beatDelta = deltaX / pixelsPerBeat;

    // Calculate new duration based on initial duration + delta (more responsive)
    const rawNewDuration = dragStart.initialDuration + beatDelta;

    // Apply quantization based on freeMovement setting
    const finalDuration = quantizeDuration(rawNewDuration);

    onUpdate({ duration: finalDuration });
  }, [isResizing, dragStart, pixelsPerBeat, onUpdate, quantizeDuration]);

  // Handle velocity move
  const handleVelocityMove = useCallback((e: MouseEvent) => {
    if (!isVelocityDragging || !dragStart) return;

    // Check if user has moved mouse significantly since initial click using refs
    if (initialClickPositionRef.current && !hasDraggedSinceClickRef.current) {
      const deltaFromInitial = Math.abs(e.clientX - initialClickPositionRef.current.x) + Math.abs(e.clientY - initialClickPositionRef.current.y);
      if (deltaFromInitial > 3) { // Lower threshold for more sensitive detection
        hasDraggedSinceClickRef.current = true;
        console.log('Velocity drag detected - double-click deletion will be prevented');
      } else {
        // Don't change velocity until user has actually moved the mouse
        return;
      }
    }

    // Only change velocity after user has started dragging
    if (hasDraggedSinceClickRef.current) {
      // Calculate velocity based on how much the mouse has moved from initial position
      const deltaY = e.clientY - dragStart.y;

      // Convert pixel movement to velocity change
      // Negative deltaY means moving up (increase velocity)
      // Positive deltaY means moving down (decrease velocity)
      const velocityDelta = -deltaY * 2; // Adjust sensitivity (2 pixels = 1 velocity unit)

      const newVelocity = (dragStart.initialVelocity || 80) + velocityDelta;
      const clampedVelocity = Math.max(1, Math.min(127, Math.round(newVelocity)));

      // Update velocity in real-time
      onUpdate({ velocity: clampedVelocity });

      // Play note with new velocity for real-time audio feedback
      audioSynth.playNote(note.pitch, 0.1, clampedVelocity);
    }
  }, [isVelocityDragging, dragStart, noteHeight, onUpdate, note.pitch]);

  // Set up global mouse event listeners
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else if (isVelocityDragging) {
      document.addEventListener('mousemove', handleVelocityMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mousemove', handleVelocityMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, isResizing, isVelocityDragging, handleMouseMove, handleResizeMove, handleVelocityMove, handleMouseUp]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
    };
  }, []);

  // Handle double click for deletion
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();

    // Prevent deletion if we're in the middle of a resize operation
    if (preventDoubleClick) {
      console.log('Double-click deletion prevented: resize operation in progress');
      return;
    }

    // Prevent deletion if user has dragged the note since the initial click (using ref for immediate access)
    if (hasDraggedSinceClickRef.current) {
      console.log('Double-click deletion prevented: note was dragged');
      return;
    }

    // Check if this is a legitimate double-click (within reasonable time)
    const currentTime = Date.now();
    const timeSinceLastClick = currentTime - lastClickTime;

    if (editorState.mode === 'select' && timeSinceLastClick > 50 && timeSinceLastClick < 500) {
      console.log('Deleting note via double-click');
      onDelete();
    } else {
      console.log('Double-click deletion prevented: invalid timing', { timeSinceLastClick, mode: editorState.mode });
    }
  }, [editorState.mode, onDelete, preventDoubleClick, lastClickTime]);

  // Handle context menu
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    // TODO: Show context menu with options like delete, duplicate, etc.
  }, []);

  return (
    <div
      ref={noteRef}
      className={`bandlab-midi-note ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''} ${isResizing ? 'resizing' : ''} ${isVelocityDragging ? 'velocity-dragging' : ''} ${showSnapPreview ? 'quantize-preview' : ''} ${isActivelyRecording ? 'recording' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: noteWidth,
        height: noteHeight,
        backgroundColor: getNoteColor(),
        opacity,
        pointerEvents: 'all'
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleContextMenu}
      title={`${getMidiNoteName(note.pitch)} - Velocity: ${velocity} - Duration: ${note.duration.toFixed(2)} beats${isActivelyRecording ? ' (Recording)' : ''}`}
    >
      {/* Note content */}
      <div className="note-content">
        {editorState.mode === 'velocity' && (
          <div className="velocity-indicator">
            {velocity}
          </div>
        )}
      </div>

      {/* Resize handle */}
      {isSelected && editorState.mode === 'select' && (
        <div
          className="resize-handle"
          onMouseDown={handleResizeStart}
        />
      )}
    </div>
  );
};

// Helper function to get note name from MIDI number
const getMidiNoteName = (midiNote: number): string => {
  const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  const octave = Math.floor(midiNote / 12) - 1;
  const noteName = noteNames[midiNote % 12];
  return `${noteName}${octave}`;
};

export default BandLabMidiNote;
