/**
 * Test for punch-in recording backward movement detection logic
 * 
 * This test verifies that the backward movement detection correctly identifies
 * when playback has moved backward (loop restart, seek, etc.) and resets
 * the overwrite tracking appropriately.
 */

interface TestScenario {
  name: string;
  lastOverwriteBeat: number | null;
  currentBeat: number;
  expectedBackwardMovement: boolean;
  description: string;
}

const testScenarios: TestScenario[] = [
  {
    name: "Initial recording start",
    lastOverwriteBeat: null,
    currentBeat: 0,
    expectedBackwardMovement: false,
    description: "First time recording starts, no previous tracking"
  },
  {
    name: "Normal forward progression",
    lastOverwriteBeat: 2.0,
    currentBeat: 2.5,
    expectedBackwardMovement: false,
    description: "Playback moving forward normally"
  },
  {
    name: "Small backward movement (tolerance)",
    lastOverwriteBeat: 2.0,
    currentBeat: 1.95,
    expectedBackwardMovement: false,
    description: "Small backward movement within tolerance (0.05 beats)"
  },
  {
    name: "Loop restart - significant backward movement",
    lastOverwriteBeat: 4.0,
    currentBeat: 0.5,
    expectedBackwardMovement: true,
    description: "Loop restarted, moved from beat 4 to beat 0.5"
  },
  {
    name: "Timeline seek backward",
    lastOverwriteBeat: 3.5,
    currentBeat: 1.0,
    expectedBackwardMovement: true,
    description: "User clicked timeline to seek backward"
  },
  {
    name: "Exact threshold boundary",
    lastOverwriteBeat: 2.0,
    currentBeat: 1.9,
    expectedBackwardMovement: true,
    description: "Exactly at the 0.1 beat threshold for backward movement"
  },
  {
    name: "Large backward jump",
    lastOverwriteBeat: 8.0,
    currentBeat: 0.0,
    expectedBackwardMovement: true,
    description: "Large backward jump from end to beginning"
  }
];

/**
 * Test function that mimics the backward movement detection logic
 */
function testBackwardMovementDetection(lastOverwriteBeat: number | null, currentBeat: number): boolean {
  // This is the exact logic from the BandLabMidiEditor component
  const hasMovedBackward = lastOverwriteBeat !== null && currentBeat < lastOverwriteBeat - 0.1;
  return hasMovedBackward;
}

/**
 * Run all test scenarios
 */
function runPunchInRecordingTests(): void {
  console.log('=== Punch-In Recording Backward Movement Detection Tests ===\n');
  
  let passedTests = 0;
  let totalTests = testScenarios.length;
  
  testScenarios.forEach((scenario, index) => {
    const result = testBackwardMovementDetection(scenario.lastOverwriteBeat, scenario.currentBeat);
    const passed = result === scenario.expectedBackwardMovement;
    
    console.log(`Test ${index + 1}: ${scenario.name}`);
    console.log(`  Description: ${scenario.description}`);
    console.log(`  Last overwrite beat: ${scenario.lastOverwriteBeat}`);
    console.log(`  Current beat: ${scenario.currentBeat}`);
    console.log(`  Expected backward movement: ${scenario.expectedBackwardMovement}`);
    console.log(`  Actual result: ${result}`);
    console.log(`  Status: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log('');
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`=== Test Results ===`);
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Backward movement detection is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the logic implementation.');
  }
}

/**
 * Example usage in browser console:
 * 
 * 1. Open browser developer tools
 * 2. Copy and paste this entire file content into the console
 * 3. Run: runPunchInRecordingTests()
 * 
 * This will verify that the backward movement detection logic is working correctly.
 */

// Export for potential use in other test files
export { testBackwardMovementDetection, runPunchInRecordingTests, testScenarios };

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment - make functions available globally for console testing
  (window as any).runPunchInRecordingTests = runPunchInRecordingTests;
  (window as any).testBackwardMovementDetection = testBackwardMovementDetection;
  
  console.log('Punch-in recording tests loaded. Run runPunchInRecordingTests() to execute tests.');
}
