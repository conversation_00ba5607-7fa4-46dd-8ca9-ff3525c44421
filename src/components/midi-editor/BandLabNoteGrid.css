/* BandLab Note Grid Styles */
.bandlab-note-grid {
  position: relative;
  flex: 1;
  background-color: #1a1a1a;
  cursor: crosshair;
  min-height: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent main container from scrolling */
}

.bandlab-note-grid.select-mode {
  cursor: default;
}

.bandlab-note-grid.add-note-mode {
  cursor: crosshair;
}

.bandlab-note-grid.velocity-mode {
  cursor: default;
}

/* Grid SVG */
.grid-svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: all;
  z-index: 1;
  background-color: #1a1a1a;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
  shape-rendering: optimizeSpeed; /* Optimize for speed over quality */
}

/* Grid Canvas for high-resolution grids */
.grid-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Grid Lines */
.grid-line {
  stroke-width: 1;
  pointer-events: none;
  /* Performance optimizations for grid lines */
  vector-effect: non-scaling-stroke;
  shape-rendering: crispEdges;
}

.grid-line.beat-line {
  stroke: #444;
  opacity: 0.7;
}

.grid-line.measure-line {
  stroke: #666;
  opacity: 1;
  stroke-width: 1.5;
}

.grid-line.subdivision-line {
  stroke: #333;
  opacity: 0.4;
  stroke-width: 0.5;
  stroke-dasharray: 2, 2;
}

.grid-line.semitone-line {
  stroke: #333;
  opacity: 0.5;
}

.grid-line.octave-line {
  stroke: #555;
  opacity: 0.8;
  stroke-width: 1.2;
}

/* Playhead */
.playhead-line {
  stroke: #00C37D;
  stroke-width: 2;
  pointer-events: none;
  z-index: 10;
}

/* Notes Container */
.notes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

/* Ruler Container - Fixed at top */
.grid-ruler-container {
  position: relative;
  flex-shrink: 0;
  height: 24px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #555;
  z-index: 15;
  overflow: hidden;
}

.grid-ruler {
  position: relative;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* Performance optimizations for smooth scrolling */
  will-change: scroll-position;
  transform: translateZ(0); /* Force hardware acceleration */
}

.grid-ruler::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.ruler-content {
  height: 100%;
  min-width: 100%;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  contain: layout style paint; /* CSS containment for better performance */
  transition: background-color 0.2s ease;
}

.ruler-content:hover {
  background-color: rgba(0, 195, 125, 0.05);
}

.ruler-mark {
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
}

.ruler-mark.measure-mark {
  padding-left: 4px;
  font-size: 11px;
  color: #ccc;
  font-weight: 500;
  border-left: 1px solid #555;
  background: transparent;
  z-index: 2;
  user-select: none;
}

.ruler-mark.subdivision-mark {
  border-left: 1px solid #444;
  width: 1px;
  opacity: 0.6;
  z-index: 1;
}

/* Optimized ruler subdivision pattern */
.ruler-subdivision-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
}

.ruler-mark.measure-mark:first-child {
  border-left: none;
}

/* Scrollable Grid Container */
.grid-scroll-container {
  flex: 1;
  overflow: auto;
  position: relative;
  background-color: #1a1a1a;
}

/* Selection Rectangle */
.selection-rectangle {
  position: absolute;
  border: 1px dashed #00C37D;
  background-color: rgba(0, 195, 125, 0.1);
  pointer-events: none;
  z-index: 8;
}

/* Scrollbar Styling */
.grid-scroll-container::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.grid-scroll-container::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.grid-scroll-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 6px;
}

.grid-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #666;
}

.grid-scroll-container::-webkit-scrollbar-corner {
  background: #1a1a1a;
}

/* Grid Background Pattern */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, #333 1px, transparent 1px),
    linear-gradient(to bottom, #2a2a2a 1px, transparent 1px);
  background-size: 40px 20px;
  opacity: 0.3;
  pointer-events: none;
}

/* High-resolution grid fallback using CSS patterns */
.grid-background-high-res {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    /* Subdivision lines */
    linear-gradient(to right, rgba(51, 51, 51, 0.3) 1px, transparent 1px),
    /* Beat lines */
    linear-gradient(to right, rgba(68, 68, 68, 0.7) 1px, transparent 1px),
    /* Measure lines */
    linear-gradient(to right, rgba(102, 102, 102, 1) 2px, transparent 2px),
    /* Semitone lines */
    linear-gradient(to bottom, rgba(51, 51, 51, 0.5) 1px, transparent 1px),
    /* Octave lines */
    linear-gradient(to bottom, rgba(85, 85, 85, 0.8) 1px, transparent 1px);
  background-size:
    10px 100%, /* Subdivision spacing */
    40px 100%, /* Beat spacing */
    160px 100%, /* Measure spacing (4 beats) */
    100% 20px, /* Semitone spacing */
    100% 240px; /* Octave spacing (12 semitones) */
  background-position:
    0 0,
    0 0,
    0 0,
    0 0,
    0 0;
  pointer-events: none;
  opacity: 0.8;
}

/* Measure Backgrounds */
.measure-background {
  pointer-events: none;
  z-index: 2;
}

/* Measure Highlights */
.measure-highlight {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.02);
  pointer-events: none;
  z-index: 2;
}

/* Beat Subdivisions */
.beat-subdivision {
  position: absolute;
  top: 0;
  height: 100%;
  border-left: 1px solid #2a2a2a;
  opacity: 0.5;
  pointer-events: none;
  z-index: 2;
}

/* Octave Highlights */
.octave-highlight {
  position: absolute;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.01);
  pointer-events: none;
  z-index: 2;
}

/* Key Signature Highlights (for Smart View) */
.key-highlight {
  position: absolute;
  left: 0;
  width: 100%;
  background-color: rgba(0, 195, 125, 0.05);
  pointer-events: none;
  z-index: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid-ruler-container {
    height: 20px;
  }

  .ruler-mark {
    font-size: 10px;
    padding-left: 2px;
  }

  .grid-scroll-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
}

/* Loading State */
.bandlab-note-grid.loading {
  opacity: 0.5;
  pointer-events: none;
}

.bandlab-note-grid.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid #333;
  border-top: 3px solid #00C37D;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 20;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
