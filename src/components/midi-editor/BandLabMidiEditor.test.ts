/**
 * Test file for BandLabMidiEditor recording functionality
 * 
 * This test verifies that the recording overwrite functionality works correctly
 * when recording notes on subsequent playbacks of the same region.
 */

import { MidiNote } from '../../types/midi';

// Mock data for testing
const mockMidiNotes: MidiNote[] = [
  { id: 'existing1', pitch: 60, startTime: 1.0, duration: 0.5, velocity: 80 }, // C4 at beat 1
  { id: 'existing2', pitch: 64, startTime: 2.0, duration: 1.0, velocity: 90 }, // E4 at beat 2
  { id: 'existing3', pitch: 67, startTime: 1.0, duration: 0.25, velocity: 85 }, // G4 at beat 1 (different pitch)
];

/**
 * Test the note overwriting logic that should be implemented in startRecordingNote
 * This simulates the filtering logic that removes overlapping notes
 */
function testNoteOverwriteLogic() {
  console.log('=== Testing Note Overwrite Logic ===');
  
  // Test case 1: Recording a note that should overwrite an existing note
  const newNotePitch = 60; // C4
  const newNoteStartTime = 1.0; // Same time as existing1
  const tolerance = 0.01;
  
  const filteredNotes = mockMidiNotes.filter(existingNote => {
    // Only consider notes with the same pitch
    if (existingNote.pitch !== newNotePitch) return true;

    // Check if the existing note overlaps with the new note's position
    const existingStart = existingNote.startTime;
    const existingEnd = existingNote.startTime + existingNote.duration;
    
    // Check if there's any overlap between the existing note and the new note position
    const hasOverlap = (existingStart <= newNoteStartTime + tolerance) && 
                      (existingEnd >= newNoteStartTime - tolerance);
    
    if (hasOverlap) {
      console.log(`🔄 Would overwrite existing note: pitch ${existingNote.pitch} at beat ${existingStart.toFixed(2)}`);
      return false; // Remove this note (it will be overwritten)
    }
    
    return true; // Keep this note
  });
  
  console.log('Original notes:', mockMidiNotes.length);
  console.log('Filtered notes:', filteredNotes.length);
  console.log('Notes removed:', mockMidiNotes.length - filteredNotes.length);
  
  // Verify that the correct note was removed
  const removedNote = mockMidiNotes.find(note => !filteredNotes.includes(note));
  if (removedNote && removedNote.id === 'existing1') {
    console.log('✅ Test 1 PASSED: Correct note was overwritten');
  } else {
    console.log('❌ Test 1 FAILED: Wrong note was overwritten or no note was overwritten');
  }
  
  // Test case 2: Recording a note that should NOT overwrite (different pitch)
  const newNotePitch2 = 62; // D4 (different pitch)
  const newNoteStartTime2 = 1.0; // Same time but different pitch
  
  const filteredNotes2 = mockMidiNotes.filter(existingNote => {
    if (existingNote.pitch !== newNotePitch2) return true;
    
    const existingStart = existingNote.startTime;
    const existingEnd = existingNote.startTime + existingNote.duration;
    const hasOverlap = (existingStart <= newNoteStartTime2 + tolerance) && 
                      (existingEnd >= newNoteStartTime2 - tolerance);
    
    return !hasOverlap;
  });
  
  if (filteredNotes2.length === mockMidiNotes.length) {
    console.log('✅ Test 2 PASSED: No notes were overwritten for different pitch');
  } else {
    console.log('❌ Test 2 FAILED: Notes were incorrectly overwritten for different pitch');
  }
  
  // Test case 3: Recording a note that should NOT overwrite (different time)
  const newNotePitch3 = 60; // C4 (same pitch)
  const newNoteStartTime3 = 3.0; // Different time
  
  const filteredNotes3 = mockMidiNotes.filter(existingNote => {
    if (existingNote.pitch !== newNotePitch3) return true;
    
    const existingStart = existingNote.startTime;
    const existingEnd = existingNote.startTime + existingNote.duration;
    const hasOverlap = (existingStart <= newNoteStartTime3 + tolerance) && 
                      (existingEnd >= newNoteStartTime3 - tolerance);
    
    return !hasOverlap;
  });
  
  if (filteredNotes3.length === mockMidiNotes.length) {
    console.log('✅ Test 3 PASSED: No notes were overwritten for different time');
  } else {
    console.log('❌ Test 3 FAILED: Notes were incorrectly overwritten for different time');
  }
}

/**
 * Test edge cases for the overwrite logic
 */
function testEdgeCases() {
  console.log('\n=== Testing Edge Cases ===');
  
  // Test case: Note that partially overlaps
  const partialOverlapNotes: MidiNote[] = [
    { id: 'partial1', pitch: 60, startTime: 1.0, duration: 1.0, velocity: 80 }, // C4 from beat 1-2
  ];
  
  const newNotePitch = 60;
  const newNoteStartTime = 1.5; // Overlaps with existing note
  const tolerance = 0.01;
  
  const filteredNotes = partialOverlapNotes.filter(existingNote => {
    if (existingNote.pitch !== newNotePitch) return true;
    
    const existingStart = existingNote.startTime;
    const existingEnd = existingNote.startTime + existingNote.duration;
    const hasOverlap = (existingStart <= newNoteStartTime + tolerance) && 
                      (existingEnd >= newNoteStartTime - tolerance);
    
    return !hasOverlap;
  });
  
  if (filteredNotes.length === 0) {
    console.log('✅ Edge Case PASSED: Partially overlapping note was correctly overwritten');
  } else {
    console.log('❌ Edge Case FAILED: Partially overlapping note was not overwritten');
  }
}

// Run the tests
if (typeof window !== 'undefined') {
  // Browser environment - run tests in console
  console.log('Running BandLabMidiEditor recording tests...');
  testNoteOverwriteLogic();
  testEdgeCases();
  console.log('\n=== Tests Complete ===');
} else {
  // Node environment - export for testing frameworks
  export { testNoteOverwriteLogic, testEdgeCases };
}
