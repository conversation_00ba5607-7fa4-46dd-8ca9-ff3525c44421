import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import './BandLabPianoKeys.css';
import { audioSynth } from '../../utils/audioSynth';

interface BandLabPianoKeysProps {
  octaveRange: { start: number; end: number };
  notePreview: boolean;
  onNotePlay?: (midiNote: number) => void;
  onNoteStop?: (midiNote: number) => void;
}

export interface PianoKeysRef {
  syncScroll: (scrollTop: number) => void;
}

interface PianoKeyData {
  midiNote: number;
  noteName: string;
  octave: number;
  isBlack: boolean;
  isC: boolean;
}

const BandLabPianoKeys = forwardRef<PianoKeysRef, BandLabPianoKeysProps>(({
  octaveRange,
  notePreview,
  onNotePlay,
  onNoteStop
}, ref) => {
  const activeNotesRef = useRef<Set<number>>(new Set());
  const contentRef = useRef<HTMLDivElement>(null);

  // Expose scroll synchronization method to parent
  useImperativeHandle(ref, () => ({
    syncScroll: (scrollTop: number) => {
      if (contentRef.current) {
        contentRef.current.style.transform = `translateY(-${scrollTop}px)`;
      }
    }
  }), []);

  // Generate piano key data
  const generateKeys = (): PianoKeyData[] => {
    const keys: PianoKeyData[] = [];
    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

    // Generate keys from highest to lowest (for display order)
    for (let octave = octaveRange.end; octave >= octaveRange.start; octave--) {
      for (let noteIndex = 11; noteIndex >= 0; noteIndex--) {
        const midiNote = octave * 12 + noteIndex + 12; // +12 because MIDI note 60 is C4
        const noteName = noteNames[noteIndex];
        const isBlack = noteName.includes('#');
        const isC = noteName === 'C';

        keys.push({
          midiNote,
          noteName,
          octave,
          isBlack,
          isC
        });
      }
    }

    return keys;
  };

  const keys = generateKeys();



  // Audio playback functions using shared synth
  const playNote = (midiNote: number) => {
    if (!notePreview) return;

    // Stop any existing note
    stopNote(midiNote);

    // Use shared audio synth for consistent sound, but don't let it auto-stop
    // We'll control the duration manually with mouse events
    audioSynth.playNote(midiNote, 10, 80, { waveform: 'triangle' }); // 10 second max duration
    activeNotesRef.current.add(midiNote);

    if (onNotePlay) {
      onNotePlay(midiNote);
    }
  };

  const stopNote = (midiNote: number) => {
    if (activeNotesRef.current.has(midiNote)) {
      audioSynth.stopNote(midiNote);
      activeNotesRef.current.delete(midiNote);

      if (onNoteStop) {
        onNoteStop(midiNote);
      }
    }
  };

  const handleMouseDown = (midiNote: number) => {
    playNote(midiNote);
  };

  const handleMouseUp = (midiNote: number) => {
    stopNote(midiNote);
  };

  const handleMouseLeave = (midiNote: number) => {
    stopNote(midiNote);
  };

  // Listen for loop events to stop currently playing piano notes
  useEffect(() => {
    const handleLoopEvent = (e: CustomEvent) => {
      // Stop all currently playing piano notes when a loop occurs
      activeNotesRef.current.forEach((midiNote) => {
        stopNote(midiNote);
      });
    };

    // Listen for the youtube-loop event
    window.addEventListener('youtube-loop', handleLoopEvent as EventListener);

    return () => {
      window.removeEventListener('youtube-loop', handleLoopEvent as EventListener);
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      activeNotesRef.current.forEach((midiNote) => {
        stopNote(midiNote);
      });
    };
  }, []);

  return (
    <div className="bandlab-piano-keys">
      <div className="piano-keys-content" ref={contentRef}>
        {keys.map((key) => (
          <div
            key={key.midiNote}
            className={`piano-key ${key.isBlack ? 'black-key' : 'white-key'} ${key.isC ? 'c-key' : ''}`}
            data-midi-note={key.midiNote}
            onMouseDown={() => handleMouseDown(key.midiNote)}
            onMouseUp={() => handleMouseUp(key.midiNote)}
            onMouseLeave={() => handleMouseLeave(key.midiNote)}
            title={`${key.noteName}${key.octave} (MIDI ${key.midiNote})`}
          >
            {key.isC && (
              <span className="note-label">
                {key.noteName}{key.octave}
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
});

BandLabPianoKeys.displayName = 'BandLabPianoKeys';

export default BandLabPianoKeys;
