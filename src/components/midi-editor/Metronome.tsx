import React, { useEffect, useRef } from 'react';

interface MetronomeProps {
  bpm: number;
  isPlaying: boolean; // Is the main transport (e.g., video/audio) playing?
  metronomeEnabled: boolean; // Is the metronome toggled on by the user?
  getCurrentVideoTime?: () => number | undefined; // Function to get current video time
  referenceStartTime: number; // Reference start time for synchronization
}

const Metronome: React.FC<MetronomeProps> = ({
  bpm,
  isPlaying,
  metronomeEnabled,
  getCurrentVideoTime,
  referenceStartTime,
}) => {
  const audioContextRef = useRef<AudioContext | null>(null);
  const nextTickTimeRef = useRef<number>(0); // Stores the transport time of the next tick
  const tickIntervalRef = useRef<number | null>(null);
  const isPlayingRef = useRef(isPlaying);
  const metronomeEnabledRef = useRef(metronomeEnabled);
  const bpmRef = useRef(bpm);
  const referenceStartTimeRef = useRef(referenceStartTime);
  const lastTransportTimeRef = useRef<number | null>(null);
  const instanceIdRef = useRef<string>(`Metronome-${Math.random().toString(36).substring(2, 7)}`);
  const scheduledTicksRef = useRef<Set<number>>(new Set()); // Track scheduled transport times to prevent duplicates

  useEffect(() => {
    isPlayingRef.current = isPlaying;
    metronomeEnabledRef.current = metronomeEnabled;
    bpmRef.current = bpm;
    referenceStartTimeRef.current = referenceStartTime;
  });

  useEffect(() => {
    // This effect ensures AudioContext is ready when metronome is enabled.
    if (metronomeEnabled) {
      if (!audioContextRef.current && typeof window !== 'undefined') {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
      if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
        audioContextRef.current.resume().catch(err => console.error(instanceIdRef.current + ': Error resuming AudioContext:', err));
      }
    }
    // Consider if context should be closed/suspended when metronomeEnabled is false
    // For now, leaving it open to avoid re-creation costs if toggled frequently.
  }, [metronomeEnabled]);

  const playTick = (time: number, transportTime: number) => { // time is the target audioContext.currentTime, transportTime is the beat time
    if (!audioContextRef.current || !audioContextRef.current.destination) {
      return;
    }

    // Check if we've already scheduled a tick for this transport time (with small tolerance for floating point precision)
    const tolerance = 0.001;
    const alreadyScheduled = Array.from(scheduledTicksRef.current).some(
      scheduledTime => Math.abs(scheduledTime - transportTime) < tolerance
    );

    if (alreadyScheduled) {
      return;
    }

    // Add this transport time to our scheduled set
    scheduledTicksRef.current.add(transportTime);

    const context = audioContextRef.current;
    const audioCtxTime = context.currentTime;
    const currentVideoTime = getCurrentVideoTime ? (getCurrentVideoTime() ?? 0) : 0; // Get current video time directly
    const refStartTime = referenceStartTimeRef.current; // Use the ref for reference start time
    const currentBpm = bpmRef.current; // Use the ref for current BPM

    const oscillator = context.createOscillator();
    const gainNode = context.createGain();

    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(880, time); // A4 pitch for a clear tick
    gainNode.gain.setValueAtTime(0.5, time); // Reduced gain
    gainNode.gain.exponentialRampToValueAtTime(0.001, time + 0.05);

    oscillator.connect(gainNode);
    gainNode.connect(context.destination);

    oscillator.start(time);
    oscillator.stop(time + 0.05);

    // Clean up old scheduled times to prevent memory leaks
    // Remove transport times that are more than 2 seconds in the past
    const cutoffTime = transportTime - 2.0;
    scheduledTicksRef.current.forEach(scheduledTime => {
      if (scheduledTime < cutoffTime) {
        scheduledTicksRef.current.delete(scheduledTime);
      }
    });
  };

  useEffect(() => {
    // Always clear any existing interval first to prevent multiple intervals
    if (tickIntervalRef.current) {
      clearInterval(tickIntervalRef.current);
      tickIntervalRef.current = null;
    }

    if (!isPlayingRef.current || !metronomeEnabledRef.current || bpmRef.current <= 0 || !audioContextRef.current || audioContextRef.current.state !== 'running') {
      nextTickTimeRef.current = 0; // Reset next tick time when metronome is stopped/disabled or context not running
      scheduledTicksRef.current.clear(); // Clear scheduled ticks when stopping
      return;
    }

    const schedulerIntervalMs = 25; // How often the scheduler function runs
    const scheduleAheadTimeSec = 0.1; // How far ahead to schedule audio events

    // Corrected initialization logic starts here
    const initialCurrentTime = getCurrentVideoTime ? (getCurrentVideoTime() ?? 0) : 0;
    const initialReferenceStartTime = referenceStartTimeRef.current;
    const initialBpm = bpmRef.current;

    // Calculate actual transport time relative to reference start
    const initialActualCurrentTransportTime = Math.max(0, initialCurrentTime - initialReferenceStartTime);
    const initialCurrentBeatDuration = 60 / initialBpm;

    if (initialCurrentBeatDuration <= 0 || isNaN(initialCurrentBeatDuration)) {

      // Clear any existing interval if we're bailing out
      if (tickIntervalRef.current) {
        clearInterval(tickIntervalRef.current);
        tickIntervalRef.current = null;
      }
      return; // Exit effect if BPM is invalid or beat duration can't be calculated
    }
    
    // Initialize nextTickTimeRef.current based on initialActualCurrentTransportTime
    // This ensures the first tick is aligned with the beat relative to the reference start time.
    if (initialActualCurrentTransportTime < 0.00001) { // Effectively zero (e.g., playback starts exactly at reference time)
        nextTickTimeRef.current = 0;
    } else {
        const remainder = initialActualCurrentTransportTime % initialCurrentBeatDuration;
        // If exactly on a beat (or very close, accounting for float precision)
        if (remainder < 0.00001 || Math.abs(remainder - initialCurrentBeatDuration) < 0.00001) { 
            nextTickTimeRef.current = initialActualCurrentTransportTime;
        } else {
            // Otherwise, schedule for the start of the *next* beat
            nextTickTimeRef.current = (Math.floor(initialActualCurrentTransportTime / initialCurrentBeatDuration) + 1) * initialCurrentBeatDuration;
        }
    }


    // Corrected initialization logic ends here
    lastTransportTimeRef.current = null; // Reset for this new interval instance
    tickIntervalRef.current = setInterval(() => {
      if (!audioContextRef.current || !isPlayingRef.current || !metronomeEnabledRef.current || bpmRef.current <= 0) {

        // Condition changed mid-interval, clear and stop
        if(tickIntervalRef.current) clearInterval(tickIntervalRef.current);
        tickIntervalRef.current = null;
        nextTickTimeRef.current = 0;
        scheduledTicksRef.current.clear(); // Clear scheduled ticks when stopping mid-execution
        return;
      }

      const currentBeatDuration = 60 / bpmRef.current;
      if (currentBeatDuration <= 0 || isNaN(currentBeatDuration)) {
        if (tickIntervalRef.current) clearInterval(tickIntervalRef.current);
        tickIntervalRef.current = null;
        return;
      }

      const audioCtxTime = audioContextRef.current.currentTime;
      const currentVideoTime = getCurrentVideoTime ? (getCurrentVideoTime() ?? 0) : 0;
      const actualCurrentTransportTime = currentVideoTime - referenceStartTimeRef.current;

      if (lastTransportTimeRef.current === null) {
        lastTransportTimeRef.current = actualCurrentTransportTime;
        // The nextTickTimeRef was already initialized when the useEffect ran.
        // This first run of the interval is primarily to prime lastTransportTimeRef.
        return; // Prime and exit for this first tick, scheduling will start from the next tick.
      }

      // If transport time jumped significantly (e.g., user seeks video)
      // re-synchronize nextTickTimeRef.current
      const transportJumpThreshold = 2.0; // 2 seconds - more reasonable threshold
      const timeDifference = Math.abs(actualCurrentTransportTime - (lastTransportTimeRef.current ?? actualCurrentTransportTime));
      if (timeDifference > transportJumpThreshold) {
        let newResyncedTickTime = Math.ceil(actualCurrentTransportTime / currentBeatDuration) * currentBeatDuration;
        if (actualCurrentTransportTime === 0) newResyncedTickTime = 0;
        else if (newResyncedTickTime < actualCurrentTransportTime) newResyncedTickTime = (Math.floor(actualCurrentTransportTime / currentBeatDuration) + 1) * currentBeatDuration;
        if (newResyncedTickTime < actualCurrentTransportTime && actualCurrentTransportTime - newResyncedTickTime < 0.001) {
             newResyncedTickTime += currentBeatDuration;
        }
        nextTickTimeRef.current = newResyncedTickTime;
        // Clear scheduled ticks when transport jumps to prevent duplicates
        scheduledTicksRef.current.clear();
      }

      // Loop to schedule upcoming beats
      while (nextTickTimeRef.current < actualCurrentTransportTime + scheduleAheadTimeSec) {
        if (nextTickTimeRef.current < actualCurrentTransportTime - currentBeatDuration) { // If too far behind
            // Fast-forward nextTickTimeRef to catch up to the current time or slightly after
            let catchUpTickTime = Math.ceil(actualCurrentTransportTime / currentBeatDuration) * currentBeatDuration;
            if (actualCurrentTransportTime === 0) catchUpTickTime = 0;
            else if (catchUpTickTime < actualCurrentTransportTime) catchUpTickTime = (Math.floor(actualCurrentTransportTime / currentBeatDuration) + 1) * currentBeatDuration;
            nextTickTimeRef.current = catchUpTickTime;
            // Ensure it's not stuck if actualCurrentTransportTime isn't advancing
            if (nextTickTimeRef.current < actualCurrentTransportTime && actualCurrentTransportTime - nextTickTimeRef.current < 0.001) {
                 nextTickTimeRef.current += currentBeatDuration;
            }
            if (nextTickTimeRef.current < actualCurrentTransportTime) continue; // Skip this iteration if still behind after catchup
        }
        
        const scheduledPlayTime = nextTickTimeRef.current + referenceStartTimeRef.current - currentVideoTime + audioCtxTime;
        if (scheduledPlayTime > audioCtxTime) {
          playTick(scheduledPlayTime, nextTickTimeRef.current);
        } else if (scheduledPlayTime > audioCtxTime - 0.020) { // Slightly late, play ASAP
          playTick(audioCtxTime + 0.001, nextTickTimeRef.current); // Play almost immediately
        }
        
        nextTickTimeRef.current += currentBeatDuration;
        if (currentBeatDuration <= 0) break; // Safety break for invalid beat duration
      }

      // Update lastTransportTimeRef for next iteration
      lastTransportTimeRef.current = actualCurrentTransportTime;
    }, schedulerIntervalMs);

    return () => {
      if (tickIntervalRef.current) {
        clearInterval(tickIntervalRef.current);
        tickIntervalRef.current = null;
      }
      scheduledTicksRef.current.clear(); // Clear scheduled ticks on cleanup
      // nextTickTimeRef.current = 0; // Reset when effect cleans up, ensuring re-initialization
    };
  // Dependencies: These should cause the interval to reset and re-initialize.
  // isPlaying, metronomeEnabled, bpm are the primary controls.
  // referenceStartTime is included to restart when reference changes.
  }, [isPlaying, metronomeEnabled, bpm, referenceStartTime]);

  return null; // This component does not render any DOM elements
};

export default Metronome;
