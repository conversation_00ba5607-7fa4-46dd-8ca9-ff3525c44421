/* BandLab Piano Keys Styles */
.bandlab-piano-keys {
  display: flex;
  flex-direction: column;
  background-color: #2a2a2a;
  border-right: 1px solid #444;
  width: 120px;
  min-width: 120px;
  /* Remove independent scrolling - will be controlled by parent */
  overflow: hidden;
  user-select: none;
  /* Add top padding to align with note grid ruler */
  padding-top: 24px;
  /* Add top border to match ruler styling */
  border-top: 1px solid #555;
  /* Ensure consistent box-sizing */
  box-sizing: border-box;
  /* Position relative for scroll transform */
  position: relative;
}

.piano-key {
  position: relative;
  height: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #333;
  transition: background-color 0.1s ease;
  font-size: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.piano-key:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.piano-key:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* White Keys */
.piano-key.white-key {
  background-color: #3a3a3a;
  color: #e0e0e0;
  border-right: 1px solid #555;
  justify-content: flex-end;
  padding-right: 8px;
}

.piano-key.white-key:hover {
  background-color: #4a4a4a;
}

.piano-key.white-key:active {
  background-color: #5a5a5a;
}

/* Black Keys */
.piano-key.black-key {
  background-color: #1a1a1a;
  color: #fff;
  width: 75%;
  margin-left: auto;
  border-right: 1px solid #333;
  justify-content: flex-end;
  padding-right: 6px;
  z-index: 1;
}

.piano-key.black-key:hover {
  background-color: #2a2a2a;
}

.piano-key.black-key:active {
  background-color: #3a3a3a;
}

/* C Key Highlighting */
.piano-key.c-key {
  border-left: 3px solid #00C37D;
  font-weight: 600;
  /* Add subtle right border to help visualize alignment */
  border-right: 1px solid #00C37D;
}

.piano-key.c-key.black-key {
  border-left: 2px solid #00C37D;
}

/* Note Labels */
.note-label {
  font-size: 9px;
  font-weight: 600;
  opacity: 0.8;
  pointer-events: none;
}

.piano-key.black-key .note-label {
  color: #ccc;
}

/* Piano keys content container for scroll synchronization */
.piano-keys-content {
  display: flex;
  flex-direction: column;
  transition: transform 0.1s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bandlab-piano-keys {
    width: 80px;
    min-width: 80px;
    /* Match mobile ruler height */
    padding-top: 20px;
  }
  
  .piano-key {
    height: 16px;
    font-size: 8px;
  }
  
  .piano-key.white-key {
    padding-right: 4px;
  }
  
  .piano-key.black-key {
    padding-right: 3px;
    width: 70%;
  }
  
  .note-label {
    font-size: 7px;
  }
}

/* Scrollbar styling for webkit browsers */
.bandlab-piano-keys::-webkit-scrollbar {
  width: 6px;
}

.bandlab-piano-keys::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.bandlab-piano-keys::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 3px;
}

.bandlab-piano-keys::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Animation for key press */
@keyframes keyPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.piano-key:active {
  animation: keyPress 0.1s ease;
}
