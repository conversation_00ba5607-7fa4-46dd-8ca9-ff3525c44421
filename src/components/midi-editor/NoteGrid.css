.note-grid-container {
  position: relative; /* For absolute positioning of playhead and children like notes-area */
  flex-grow: 1;
  background-color: #2a2a2e; /* Slightly different from piano keys */
  display: grid; /* Use grid for layering lines and notes */
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  overflow: auto; /* Enable scrolling if content exceeds dimensions */
}

.pitch-lines-wrapper,
.time-lines-wrapper,
.notes-area {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
}

.pitch-lines-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Distribute lines evenly */
  pointer-events: none; /* Lines should not interfere with mouse events for notes */
}

.pitch-line {
  width: 100%;
  height: 1px;
  background-color: #404045; /* Darker lines */
}

.pitch-line.octave-line {
  background-color: #505055; /* Slightly more prominent octave lines */
}

.time-lines-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between; /* Distribute lines evenly */
  pointer-events: none;
}

.time-line {
  height: 100%;
  width: 1px;
}

.beat-line {
  background-color: #404045;
}

.measure-line {
  background-color: #505055;
}

.notes-area {
  position: relative; /* Notes will be positioned absolutely within this */
  /* background-color: rgba(0,0,0,0.1); /* For debugging */
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px; /* Adjust thickness as needed */
  background-color: red;
  z-index: 10; /* Ensure it's above notes and grid lines */
  pointer-events: none; /* So it doesn't interfere with mouse events on notes */
}
