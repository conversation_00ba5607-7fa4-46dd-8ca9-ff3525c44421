.midi-editor-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #444;
  background-color: #282c34;
  color: white;
  padding: 16px;
  min-height: 400px; /* Adjust as needed */
  overflow: hidden; /* Prevent content from spilling out before scroll is implemented */
}

.midi-editor-container h2 {
  margin-top: 0;
  color: #61dafb;
}

/* Placeholder for future styles */

.midi-editor-layout {
  display: flex;
  flex-direction: row; /* Keys on left, grid on right */
  flex-grow: 1; /* Allow layout to fill container */
  background-color: #222; /* Darker background for the layout area */
  overflow: auto; /* Add scroll for content overflow */
}

.piano-keys-container {
  /* Styles already defined in PianoKeys.css, but can be overridden or extended here */
  /* Example: width: 120px; */
  flex-shrink: 0; /* Prevent piano keys from shrinking */
}

.note-grid-area {
  flex-grow: 1; /* Allow note grid to take remaining space */
  padding: 10px;
  color: #ccc;
  /* background-color: #1e1e1e; /* Slightly different background for grid */
  overflow: auto; /* In case grid content is very large */
}

.midi-controls {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f0f0f0; /* Light grey background for controls area */
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;
  gap: 20px; /* Spacing between control items */
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px; /* Spacing within a control item, e.g., between label and input */
}

.control-item label {
  font-weight: bold;
}

.control-item input[type="number"] {
  width: 60px; /* Adjust width as needed */
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.control-item button {
  padding: 8px 12px;
  background-color: #007bff; /* Blue background */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.control-item button:hover {
  background-color: #0056b3; /* Darker blue on hover */
}

.control-item p {
  margin: 0;
  font-size: 0.9em;
  color: #333;
}

/* .piano-roll-panel {
  Styles for the main area containing keys and note grid
} */

/* .midi-keys {
  Styles for the piano keys section
} */

/* .note-grid {
  Styles for the grid where notes are placed
} */

/* .midi-note {
  Styles for individual MIDI notes
} */
