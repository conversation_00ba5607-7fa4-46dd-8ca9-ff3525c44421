.piano-keys-container {
  display: flex;
  flex-direction: column-reverse; /* Lowest pitch at the bottom, rendering keys upwards */
  background-color: #e0e0e0; /* Light grey background for the key area */
  border-right: 1px solid #999; /* Separator from the note grid */
  width: 80px; /* Fixed width for the piano key panel */
}

.piano-key {
  box-sizing: border-box;
  height: 15px; /* Each key (semitone) occupies this height, matching pixelsPerPitch */
  display: flex;
  align-items: center;
  justify-content: flex-end; /* Align note name text to the right (towards the grid) */
  padding-right: 5px;
  font-size: 9px;
  font-family: Arial, sans-serif;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #d0d0d0; /* Separator line for each semitone */
}


.piano-key:hover {
  background-color: #c5c5c5; /* Slight hover effect */
}

.white-key {
  background-color: #ffffff;
  color: #333;
  width: 100%; /* White keys take the full width of the container */
}

.white-key.highlight-c {
  /* Optional: slightly different background for C notes for better orientation */
  /* background-color: #f0f0f0; */
  border-left: 3px solid #b0b0b0; /* Emphasize C notes a bit */
}

.black-key {
  background-color: #333333;
  color: #ffffff;
  width: 65%; /* Black keys are visually shorter (less wide in this orientation) */
  margin-left: auto; /* Pushes the black key to the right side of its 15px slot */
  border-left: 1px solid #555;
  border-bottom-color: #555; /* Darker border for black keys */
}

/* Remove individual note name display from PianoKey.tsx if not desired, */
/* or style it here if kept. */
/* .note-name {
  padding-left: 5px;
} */
