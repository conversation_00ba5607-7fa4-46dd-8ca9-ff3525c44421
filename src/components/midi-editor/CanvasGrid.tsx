import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import { useCanvasPerformance } from '../../hooks/useCanvasPerformance';

interface CanvasGridProps {
  width: number;
  height: number;
  pixelsPerBeat: number;
  pixelsPerSemitone: number;
  beatsPerMeasure: number;
  totalBeats: number;
  totalSemitones: number;
  quantizeValue: string;
  className?: string;
  // Viewport culling for performance
  viewportX?: number;
  viewportY?: number;
  viewportWidth?: number;
  viewportHeight?: number;
}

const CanvasGrid: React.FC<CanvasGridProps> = ({
  width,
  height,
  pixelsPerBeat,
  pixelsPerSemitone,
  beatsPerMeasure,
  totalBeats,
  totalSemitones,
  quantizeValue,
  className,
  viewportX = 0,
  viewportY = 0,
  viewportWidth = width,
  viewportHeight = height
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const isInitialRenderRef = useRef(true);
  const lastQuantizeValueRef = useRef(quantizeValue);

  // Use performance optimization hook
  const {
    debouncedRender,
    getOptimizedCanvasSize,
    getVisibleBounds,
    getMetrics
  } = useCanvasPerformance({
    debounceMs: 16, // 60fps limit
    maxCanvasSize: 8192,
    enableMetrics: process.env.NODE_ENV === 'development'
  });

  // Memoize expensive calculations with performance optimizations
  const gridParams = useMemo(() => {
    const quantizeFraction = quantizeValue.split('/');
    let subdivisionsPerBeat = 1;

    if (quantizeValue !== 'free' && quantizeFraction.length === 2) {
      const denominator = parseInt(quantizeFraction[1]);
      subdivisionsPerBeat = denominator / 4;
    }

    // Use optimized viewport culling with fallback for initial render
    const beatBounds = getVisibleBounds(
      viewportX,
      0, // Use 0 for Y since we're calculating horizontal bounds
      viewportWidth,
      viewportHeight,
      pixelsPerBeat,
      totalBeats,
      4 // Larger buffer for initial renders
    );

    const semitoneBounds = getVisibleBounds(
      viewportY,
      0, // Use 0 for X since we're calculating vertical bounds
      viewportHeight,
      viewportWidth,
      pixelsPerSemitone,
      totalSemitones,
      4 // Larger buffer for initial renders
    );

    // Ensure we always render some content, especially on initial load
    const minBeatsToShow = Math.min(16, totalBeats); // Show at least 16 beats (4 measures)
    const minSemitonesToShow = Math.min(36, totalSemitones); // Show at least 3 octaves

    // Special handling for initial viewport (0,0) - ensure we show content
    const isInitialViewport = viewportX === 0 && viewportY === 0;

    const finalBeatBounds = {
      startIndex: isInitialViewport ? 0 : Math.max(0, Math.min(beatBounds.startIndex, totalBeats - minBeatsToShow)),
      endIndex: isInitialViewport ? minBeatsToShow : Math.min(totalBeats, Math.max(beatBounds.endIndex, minBeatsToShow))
    };

    const finalSemitoneBounds = {
      startIndex: isInitialViewport ? 0 : Math.max(0, Math.min(semitoneBounds.startIndex, totalSemitones - minSemitonesToShow)),
      endIndex: isInitialViewport ? minSemitonesToShow : Math.min(totalSemitones, Math.max(semitoneBounds.endIndex, minSemitonesToShow))
    };

    // Get optimized canvas size
    const canvasSize = getOptimizedCanvasSize(width, height);

    return {
      subdivisionsPerBeat,
      startBeat: finalBeatBounds.startIndex,
      endBeat: finalBeatBounds.endIndex,
      startSemitone: finalSemitoneBounds.startIndex,
      endSemitone: finalSemitoneBounds.endIndex,
      canvasSize,
      // Create a hash for change detection
      hash: `${width}-${height}-${pixelsPerBeat}-${pixelsPerSemitone}-${quantizeValue}-${viewportX}-${viewportY}-${viewportWidth}-${viewportHeight}`
    };
  }, [width, height, pixelsPerBeat, pixelsPerSemitone, quantizeValue, viewportX, viewportY, viewportWidth, viewportHeight, totalBeats, totalSemitones, getVisibleBounds, getOptimizedCanvasSize]);

  const drawGridOptimized = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Use optimized canvas size from performance hook
    const { canvasSize } = gridParams;
    const effectiveWidth = canvasSize.width;
    const effectiveHeight = canvasSize.height;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = effectiveWidth * dpr;
    canvas.height = effectiveHeight * dpr;
    canvas.style.width = `${effectiveWidth}px`;
    canvas.style.height = `${effectiveHeight}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas and set background
    ctx.clearRect(0, 0, effectiveWidth, effectiveHeight);

    // Set background color to match the grid background
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, effectiveWidth, effectiveHeight);

    // Use optimized parameters
    const { subdivisionsPerBeat, startBeat, endBeat, startSemitone, endSemitone } = gridParams;

    // Optimize drawing with batched operations and viewport culling

    // Draw vertical lines (time) - only visible ones
    ctx.beginPath();

    // Measure lines - only draw visible measures
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1.5;
    const startMeasure = Math.max(0, Math.floor(startBeat / beatsPerMeasure));
    const endMeasure = Math.min(Math.ceil(totalBeats / beatsPerMeasure), Math.ceil(endBeat / beatsPerMeasure));

    for (let measure = startMeasure; measure <= endMeasure; measure++) {
      const x = measure * beatsPerMeasure * pixelsPerBeat;
      if (x >= 0 && x <= effectiveWidth) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, effectiveHeight);
      }
    }
    ctx.stroke();

    // Beat lines - only draw visible beats
    ctx.beginPath();
    ctx.strokeStyle = '#444';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.7;
    for (let beat = startBeat; beat <= endBeat; beat++) {
      const x = beat * pixelsPerBeat;
      if (x >= 0 && x <= effectiveWidth && beat % beatsPerMeasure !== 0) { // Skip measure lines
        ctx.moveTo(x, 0);
        ctx.lineTo(x, effectiveHeight);
      }
    }
    ctx.stroke();

    // Subdivision lines - optimized for high performance
    if (quantizeValue !== 'free' && subdivisionsPerBeat > 1) {
      ctx.beginPath();
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 0.5;

      // Reduce opacity for very high subdivisions to reduce visual noise
      const opacity = subdivisionsPerBeat >= 16 ? 0.2 : subdivisionsPerBeat >= 8 ? 0.3 : 0.4;
      ctx.globalAlpha = opacity;
      ctx.setLineDash([2, 2]);

      // Limit subdivision rendering for extreme zoom levels
      const maxSubdivisions = subdivisionsPerBeat > 32 ? 32 : subdivisionsPerBeat;
      const subdivisionStep = subdivisionsPerBeat > 32 ? subdivisionsPerBeat / 32 : 1;

      for (let beat = startBeat; beat < endBeat; beat++) {
        for (let sub = 1; sub < maxSubdivisions; sub += subdivisionStep) {
          const x = beat * pixelsPerBeat + (sub * pixelsPerBeat / subdivisionsPerBeat);
          if (x >= 0 && x <= effectiveWidth) {
            ctx.moveTo(x, 0);
            ctx.lineTo(x, effectiveHeight);
          }
        }
      }
      ctx.stroke();
      ctx.setLineDash([]); // Reset line dash
    }

    // Draw horizontal lines (pitch) - only visible ones
    ctx.globalAlpha = 1;

    // Octave lines - only draw visible octaves
    ctx.beginPath();
    ctx.strokeStyle = '#555';
    ctx.lineWidth = 1.2;
    ctx.globalAlpha = 0.8;
    const startOctave = Math.max(0, Math.floor(startSemitone / 12) * 12);
    const endOctave = Math.min(totalSemitones, Math.ceil(endSemitone / 12) * 12);

    for (let semitone = startOctave; semitone <= endOctave; semitone += 12) {
      const y = semitone * pixelsPerSemitone;
      if (y >= 0 && y <= effectiveHeight) {
        ctx.moveTo(0, y);
        ctx.lineTo(effectiveWidth, y);
      }
    }
    ctx.stroke();

    // Semitone lines - only draw visible semitones
    ctx.beginPath();
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.globalAlpha = 0.5;
    for (let semitone = startSemitone; semitone <= endSemitone; semitone++) {
      const y = semitone * pixelsPerSemitone;
      if (y >= 0 && y <= effectiveHeight && semitone % 12 !== 0) { // Skip octave lines
        ctx.moveTo(0, y);
        ctx.lineTo(effectiveWidth, y);
      }
    }
    ctx.stroke();

    // Reset alpha
    ctx.globalAlpha = 1;
  }, [gridParams]);

  // Use the performance-optimized debounced render, but render immediately for critical changes
  useEffect(() => {
    // Determine if this should be an immediate render
    const isQuantizeChange = lastQuantizeValueRef.current !== quantizeValue;
    const isInitialRender = isInitialRenderRef.current;
    const shouldRenderImmediate = isInitialRender || isQuantizeChange;



    // Update refs
    if (isInitialRenderRef.current) {
      isInitialRenderRef.current = false;
    }
    lastQuantizeValueRef.current = quantizeValue;

    debouncedRender(drawGridOptimized, gridParams.hash, shouldRenderImmediate);
  }, [debouncedRender, drawGridOptimized, gridParams.hash, quantizeValue]);

  // Additional effect to ensure immediate render when Canvas is first mounted
  useEffect(() => {
    // Force immediate render on mount
    if (canvasRef.current) {
      drawGridOptimized();
    }
  }, []); // Empty dependency array - only run on mount



  return (
    <canvas
      ref={canvasRef}
      className={className}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        pointerEvents: 'none',
        zIndex: 1
      }}
    />
  );
};

export default CanvasGrid;
