/* BandLab MIDI Note Styles */
.bandlab-midi-note {
  position: absolute;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.1s ease;
  user-select: none;
  overflow: hidden;
  min-width: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.bandlab-midi-note:hover {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  z-index: 10;
}

.bandlab-midi-note.selected {
  border-color: #ffffff;
  border-width: 2px;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3), 0 2px 8px rgba(0, 0, 0, 0.5);
  z-index: 15;
}

.bandlab-midi-note.dragging {
  cursor: grabbing;
  z-index: 20;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
}

.bandlab-midi-note.resizing {
  cursor: ew-resize;
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  border-color: rgba(255, 255, 255, 0.8);
}

.bandlab-midi-note.velocity-dragging {
  cursor: ns-resize;
  z-index: 20;
  transform: scaleY(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  border-color: rgba(255, 255, 255, 0.8);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.bandlab-midi-note.resizing .resize-handle {
  opacity: 1 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  width: 10px;
  right: -4px;
  box-shadow: 0 0 6px rgba(255, 255, 255, 0.5);
}

/* Note Content */
.note-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
}

/* Velocity Indicator */
.velocity-indicator {
  font-size: 10px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

/* Resize Handle */
.resize-handle {
  position: absolute;
  right: -3px;
  top: 0;
  bottom: 0;
  width: 8px;
  background-color: rgba(255, 255, 255, 0.4);
  cursor: ew-resize;
  opacity: 0;
  transition: all 0.2s ease;
  border-radius: 0 3px 3px 0;
  border-left: 1px solid rgba(255, 255, 255, 0.6);
}

.bandlab-midi-note.selected .resize-handle {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.6);
}

.bandlab-midi-note:hover .resize-handle {
  opacity: 0.8;
  background-color: rgba(255, 255, 255, 0.5);
}

.resize-handle:hover {
  background-color: rgba(255, 255, 255, 0.8) !important;
  width: 10px;
  right: -4px;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

/* Resize handle visual indicator */
.resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 60%;
  background: repeating-linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.8) 0px,
    rgba(255, 255, 255, 0.8) 1px,
    transparent 1px,
    transparent 3px
  );
  border-radius: 1px;
}

/* Note Gradient Effect */
.bandlab-midi-note::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
  border-radius: 3px 3px 0 0;
}

/* Note Border Highlight */
.bandlab-midi-note::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  pointer-events: none;
}

/* Different note states */
.bandlab-midi-note.muted {
  opacity: 0.3;
  filter: grayscale(0.5);
}

.bandlab-midi-note.ghost {
  opacity: 0.4;
  border-style: dashed;
}

/* Velocity mode specific styles - only apply cursor when hovering over notes */
.bandlab-note-grid.velocity-mode .bandlab-midi-note {
  cursor: ns-resize;
}

.bandlab-note-grid.velocity-mode .bandlab-midi-note:hover {
  transform: scaleY(1.1);
}

/* Animation for note creation */
@keyframes noteCreate {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.bandlab-midi-note.creating {
  animation: noteCreate 0.2s ease-out;
}

/* Animation for note deletion */
@keyframes noteDelete {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

.bandlab-midi-note.deleting {
  animation: noteDelete 0.15s ease-in;
}

/* Note selection highlight */
.bandlab-midi-note.selected::before {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bandlab-midi-note {
    min-width: 6px;
    border-radius: 2px;
  }

  .velocity-indicator {
    font-size: 8px;
  }

  .resize-handle {
    width: 12px; /* Larger for touch devices */
    right: -5px;
  }

  .resize-handle:hover {
    width: 14px;
    right: -6px;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .bandlab-midi-note {
    border-width: 2px;
    border-color: #ffffff;
  }
  
  .bandlab-midi-note.selected {
    border-width: 3px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .bandlab-midi-note {
    transition: none;
  }
  
  .bandlab-midi-note.creating,
  .bandlab-midi-note.deleting {
    animation: none;
  }
}

/* Focus styles for accessibility */
.bandlab-midi-note:focus {
  outline: 2px solid #00C37D;
  outline-offset: 2px;
}

/* Note overlapping styles */
.bandlab-midi-note.overlapping {
  border-color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.8) !important;
}

/* Note quantization preview */
.bandlab-midi-note.quantize-preview {
  border-style: dashed;
  border-color: #ffd93d;
  box-shadow: 0 0 8px rgba(255, 217, 61, 0.4), 0 2px 8px rgba(0, 0, 0, 0.5);
}

/* Enhanced resize feedback when snapping to grid */
.bandlab-midi-note.resizing.quantize-preview {
  border-color: #ffd93d;
  box-shadow: 0 0 12px rgba(255, 217, 61, 0.6), 0 4px 12px rgba(0, 0, 0, 0.6);
}

.bandlab-midi-note.resizing.quantize-preview .resize-handle {
  background-color: rgba(255, 217, 61, 0.8) !important;
  box-shadow: 0 0 8px rgba(255, 217, 61, 0.6);
}

/* Recording note styles */
.bandlab-midi-note.recording {
  border-color: #ff4444;
  box-shadow: 0 0 8px rgba(255, 68, 68, 0.6), 0 2px 8px rgba(0, 0, 0, 0.5);
  animation: recordingPulse 1s ease-in-out infinite alternate;
}

.bandlab-midi-note.recording::before {
  background: linear-gradient(to bottom, rgba(255, 68, 68, 0.3), rgba(255, 68, 68, 0.1));
}

.bandlab-midi-note.recording::after {
  border-color: rgba(255, 68, 68, 0.4);
}

/* Recording pulse animation */
@keyframes recordingPulse {
  0% {
    box-shadow: 0 0 8px rgba(255, 68, 68, 0.6), 0 2px 8px rgba(0, 0, 0, 0.5);
    border-color: #ff4444;
  }
  100% {
    box-shadow: 0 0 16px rgba(255, 68, 68, 0.8), 0 2px 12px rgba(0, 0, 0, 0.6);
    border-color: #ff6666;
  }
}



/* Disable recording animation in reduced motion mode */
@media (prefers-reduced-motion: reduce) {
  .bandlab-midi-note.recording {
    animation: none;
  }
}
