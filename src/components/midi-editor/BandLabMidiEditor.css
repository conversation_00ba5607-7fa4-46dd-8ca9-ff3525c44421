/* BandLab MIDI Editor Styles */
.bandlab-midi-editor {
  --accent-color: #00C37D;
  --accent-color-bis: #00C37Dfc;
  --background-color: #1a1a1a;
  --panel-background: #2a2a2a;
  --border-color: #444;
  --text-color: #ffffff;
  --text-secondary: #b0b0b0;
  --grid-line-color: #333;
  --grid-line-secondary: #555;
  --button-background: #3a3a3a;
  --button-hover: #4a4a4a;
  --input-background: #2a2a2a;
  --input-border: #555;
  
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 13px;
}

.midi-editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: var(--background-color);
}

/* Panel Header Styles */
.midi-editor-panel-header {
  background-color: var(--panel-background);
  border-bottom: 1px solid var(--border-color);
  padding: 8px 16px;
}

.midi-editor-track-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.midi-editor-track-icon {
  color: var(--accent-color);
  display: flex;
  align-items: center;
}

.midi-editor-track-title {
  font-weight: 600;
  color: var(--text-color);
  flex: 1;
}

.midi-editor-header-controls {
  display: flex;
  gap: 4px;
}

/* Octave Range Controls */
.octave-range-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 280px;
}

.octave-range-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.range-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.range-value {
  color: var(--accent-color);
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

.octave-range-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.range-control-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.range-control-label {
  font-size: 10px;
  color: var(--text-secondary);
  min-width: 30px;
}

.octave-value {
  font-size: 11px;
  color: var(--text-color);
  font-variant-numeric: tabular-nums;
  min-width: 24px;
  text-align: center;
}

.octave-presets {
  display: flex;
  gap: 4px;
}

.midi-editor-button.preset {
  font-size: 10px;
  padding: 3px 6px;
  min-width: 40px;
  background-color: var(--input-background);
  border-color: var(--input-border);
}

.midi-editor-button.preset:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.midi-editor-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--input-background);
}

.midi-editor-button:disabled:hover {
  background-color: var(--input-background);
}

/* Status Bar Styles */
.midi-editor-status-bar {
  background-color: var(--panel-background);
  border-top: 1px solid var(--border-color);
  padding: 6px 16px;
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 11px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.status-value {
  color: var(--text-color);
  font-weight: 400;
}

/* Header Styles */
.midi-editor-header {
  background-color: var(--panel-background);
  border-bottom: 1px solid var(--border-color);
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.midi-editor-header-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.midi-editor-header-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
  border-right: 1px solid var(--border-color);
}

.midi-editor-header-section:last-child {
  border-right: none;
}

.midi-editor-header-title {
  font-weight: 600;
  color: var(--text-color);
  margin-right: 8px;
}

/* Button Styles */
.midi-editor-button {
  background-color: var(--button-background);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.midi-editor-button:hover {
  background-color: var(--button-hover);
}

.midi-editor-button.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.midi-editor-button.compact {
  padding: 4px 8px;
  font-size: 11px;
}

.midi-editor-button.icon-only {
  padding: 6px;
  min-width: 32px;
  justify-content: center;
}

.midi-editor-button.recording {
  background-color: #ff4444;
  border-color: #ff4444;
  color: white;
  animation: recording-pulse 1.5s ease-in-out infinite;
}

.midi-editor-button.recording:hover {
  background-color: #ff6666;
  border-color: #ff6666;
}

@keyframes recording-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Segmented Control */
.segmented-control {
  display: flex;
  background-color: var(--input-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.segmented-control button {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid var(--border-color);
}

.segmented-control button:last-child {
  border-right: none;
}

.segmented-control button:hover {
  background-color: var(--button-hover);
}

.segmented-control button[aria-pressed="true"] {
  background-color: var(--accent-color);
  color: white;
}

/* Input Styles */
.midi-editor-input {
  background-color: var(--input-background);
  border: 1px solid var(--input-border);
  color: var(--text-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  width: 60px;
}

.midi-editor-input:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Slider Styles */
.midi-editor-slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.midi-editor-slider {
  flex: 1;
  height: 4px;
  background-color: var(--input-background);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
}

.midi-editor-slider-fill {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 2px;
  position: relative;
}

.midi-editor-slider-thumb {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: var(--accent-color);
  border-radius: 50%;
  cursor: grab;
}

.midi-editor-slider-thumb:active {
  cursor: grabbing;
}

/* Dropdown Styles */
.midi-editor-dropdown {
  background-color: var(--input-background);
  border: 1px solid var(--input-border);
  color: var(--text-color);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.midi-editor-dropdown:hover {
  background-color: var(--button-hover);
}

/* Switch Styles */
.midi-editor-switch {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: var(--input-background);
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.midi-editor-switch.active {
  background-color: var(--accent-color);
}

.midi-editor-switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.midi-editor-switch.active .midi-editor-switch-thumb {
  transform: translateX(20px);
}

/* Utility Classes */
.row-gap-4 {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.row-gap-8 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tabular-nums {
  font-variant-numeric: tabular-nums;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Icon Styles */
.midi-editor-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.midi-editor-icon.small {
  width: 12px;
  height: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .midi-editor-header-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .midi-editor-header-section {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 8px 0;
  }
  
  .midi-editor-header-section:last-child {
    border-bottom: none;
  }
}
