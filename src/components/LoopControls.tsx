import { useState, useEffect, useCallback, useRef } from 'react';
import { useAudioStore } from '../store/useAudioStore';

// Allow suppressing blur-handling while setting end live
const LoopControls: React.FC = () => {
  const {
    duration,
    loopActive,
    loopStart,
    loopEnd,
    setLoopActive,
    setLoopStart,
    setLoopEnd
  } = useAudioStore();

  // Ref to track if we're live-updating loop end (to skip onBlur)
  const isHoldingEndRef = useRef(false);
  const endIntervalRef = useRef<number | null>(null);
  const isHoldingStartRef = useRef(false);
  const startIntervalRef = useRef<number | null>(null);

  // Format time for display (MM:SS)
  const formatTimeForDisplay = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Local state for input fields
  const [startInput, setStartInput] = useState(formatTimeForDisplay(typeof loopStart === 'number' ? loopStart : 0));
  const [endInput, setEndInput] = useState(formatTimeForDisplay(typeof loopEnd === 'number' ? loopEnd : 0));

  // Update local state when store values change
  useEffect(() => {
    setStartInput(formatTimeForDisplay(typeof loopStart === 'number' ? loopStart : 0));
    setEndInput(formatTimeForDisplay(typeof loopEnd === 'number' ? loopEnd : 0));
  }, [loopStart, loopEnd]);

  // Parse time from MM:SS format to seconds
  const parseTimeInput = (timeStr: string): number => {
    // Handle empty input
    if (!timeStr.trim()) return 0;

    // If it's already in seconds format
    if (!timeStr.includes(':')) {
      const secondsFloat = parseFloat(timeStr);
      return isNaN(secondsFloat) ? 0 : secondsFloat;
    }

    // Parse MM:SS format
    const parts = timeStr.split(':');
    if (parts.length !== 2) return 0;

    const mins = parseInt(parts[0], 10);
    const secs = parseInt(parts[1], 10);

    if (isNaN(mins) || isNaN(secs)) return 0;

    return mins * 60 + secs;
  };


  // Handle start time input change
  const handleStartInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setStartInput(e.target.value);
  }, []);

  // Handle end time input change
  const handleEndInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setEndInput(e.target.value);
  }, []);

  // Prevent blur-handler while live-setting end
  const handleApplyLoopSettings = useCallback(() => {
    if (isHoldingEndRef.current || isHoldingStartRef.current) {
      return;
    }
    // Validate & apply inputs normally
    const start = parseTimeInput(startInput);
    const end   = parseTimeInput(endInput);
    const validStart = Math.max(0, Math.min(duration, start));

    // Update start
    setStartInput(formatTimeForDisplay(validStart));
    setLoopStart(validStart);

    // Only set end and activate loop if we have a valid end time
    if (!isNaN(end) && end > 0) {
      const validEnd = Math.max(validStart + 0.1, Math.min(duration, end));
      setEndInput(formatTimeForDisplay(validEnd));
      setLoopEnd(validEnd);
    } else {
      // Clear the end input if it's invalid
      if (isNaN(end) || end <= 0) {
        setEndInput('');
      }
    }
  }, [startInput, endInput, duration, setLoopStart, setLoopEnd]);



  // Live-update loop start while holding the button
  const handleStartMouseDown = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    isHoldingStartRef.current = true;
    e.preventDefault();
    // Update loop start every 100ms based on currentTime
    startIntervalRef.current = window.setInterval(() => {
      const { currentTime: t, setLoopStart } = useAudioStore.getState();
      setStartInput(formatTimeForDisplay(t));
      setLoopStart(t); // Use setLoopStart instead of setLoopRegion
    }, 100);
    // On mouse up, clear interval and apply final value
    const stop = () => {
      if (startIntervalRef.current !== null) {
        clearInterval(startIntervalRef.current);
        startIntervalRef.current = null;
      }
      const { currentTime: t, setLoopStart } = useAudioStore.getState();
      setStartInput(formatTimeForDisplay(t));
      setLoopStart(t); // Use setLoopStart instead of setLoopRegion
      isHoldingStartRef.current = false;
      document.removeEventListener('mouseup', stop);
    };
    document.addEventListener('mouseup', stop);
  }, []);

  // Live-update loop end while holding the button
  const handleEndMouseDown = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    // Temporarily suppress looping without hiding the loop region
    isHoldingEndRef.current = true;
    // signal YouTubePlayer to skip loop while dragging
    // @ts-ignore
    window.isHoldingEndForLoop = true;
    e.preventDefault();

    // Only update the input text every 100ms; don't modify the store until release
    endIntervalRef.current = window.setInterval(() => {
      // Live-update loop end
      const { currentTime: t, setLoopEnd } = useAudioStore.getState();
      setEndInput(formatTimeForDisplay(t));
      setLoopEnd(t); // Use setLoopEnd instead of setLoopRegion
    }, 100);

    // On release, apply final loop region, restore looping, and exit live mode
    const stop = () => {
      if (endIntervalRef.current !== null) {
        clearInterval(endIntervalRef.current);
        endIntervalRef.current = null;
      }
      // Get the most accurate current time from the YouTube player
      const player = (window as any).youtubePlayerRef;
      const preciseTime: number = (
        player && typeof player.getCurrentTime === 'function'
      ) ? player.getCurrentTime() : useAudioStore.getState().currentTime;
      const { setLoopEnd } = useAudioStore.getState();
      setEndInput(formatTimeForDisplay(preciseTime));
      setLoopEnd(preciseTime); // Use setLoopEnd instead of setLoopRegion
      // restore looping behavior
      // @ts-ignore
      window.isHoldingEndForLoop = false;
      isHoldingEndRef.current = false;
      document.removeEventListener('mouseup', stop);
    };
    document.addEventListener('mouseup', stop);
  }, [loopActive, setLoopActive]);

  // Make isHoldingEndRef globally accessible for WaveformTimeline
  // @ts-ignore
  window.isHoldingEndRef = isHoldingEndRef;

  // Handle key down events for inputs
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur(); // Trigger blur to apply changes
    }
  }, []);

  return (
    <div style={{ width: '100%', maxWidth: '48rem', margin: '1rem auto', padding: '1rem', backgroundColor: '#f3f4f6', borderRadius: '0.5rem' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
        <span style={{ fontSize: '1rem', fontWeight: '500' }}>Loop Controls</span>
        <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <input
            type="checkbox"
            checked={loopActive}
            onChange={(e) => setLoopActive(e.target.checked)}
            style={{ width: '1rem', height: '1rem' }}
          />
          <span style={{ fontSize: '0.875rem' }}>Enable Loop</span>
        </label>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
        {/* Loop start controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '0.875rem', width: '4rem' }}>Start:</span>
          <input
            type="text"
            value={startInput}
            onChange={handleStartInputChange}
            onBlur={handleApplyLoopSettings}
            onKeyDown={handleKeyDown}
            style={{
              width: '5rem',
              padding: '0.25rem 0.5rem',
              fontSize: '0.875rem',
              fontFamily: 'monospace',
              textAlign: 'center',
              border: '1px solid #d1d5db',
              borderRadius: '0.25rem'
            }}
          />
          <button
            onMouseDown={handleStartMouseDown}
            style={{
              padding: '0.25rem 0.5rem',
              fontSize: '0.75rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.25rem',
              cursor: 'pointer'
            }}
          >
            Set Current
          </button>
        </div>

        {/* Loop end controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ fontSize: '0.875rem', width: '4rem' }}>End:</span>
          <input
            type="text"
            value={endInput}
            onChange={handleEndInputChange}
            onBlur={handleApplyLoopSettings}
            onKeyDown={handleKeyDown}
            style={{
              width: '5rem',
              padding: '0.25rem 0.5rem',
              fontSize: '0.875rem',
              fontFamily: 'monospace',
              textAlign: 'center',
              border: '1px solid #d1d5db',
              borderRadius: '0.25rem'
            }}
          />
          <button
            onMouseDown={handleEndMouseDown}
            style={{
              padding: '0.25rem 0.5rem',
              fontSize: '0.75rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.25rem',
              cursor: 'pointer'
            }}
          >
            Set Current
          </button>
        </div>
      </div>

      {/* Loop active status display removed */}
    </div>
  );
};

export default LoopControls;
