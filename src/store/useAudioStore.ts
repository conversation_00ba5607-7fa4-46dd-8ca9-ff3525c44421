import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Extract the YouTube video ID from a URL.
 * Matches typical YouTube URL formats.
 */
const extractYouTubeId = (url: string | null): string | null => {
  if (!url) return null;
  const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
  const match = url.match(regExp);
  return (match && match[7].length === 11) ? match[7] : null;
};

/**
 * Deduplicate + validate loops.
 * A loop is valid iff it has a unique `id`, numeric `start`,
 * and if `end` is provided, `start < end`.
 * Loops can now have just a start time (partial loops).
 */
const sanitizeLoops = (loops: any[]): any[] => {
  const byId   = new Set<string>();
  const bySpan = new Set<string>(); // `start-end` rounded to ms

  return (loops ?? []).filter(l => {
    if (
      !l ||
      typeof l.start !== 'number' ||
      !Number.isFinite(l.start) ||
      !l.id ||
      byId.has(l.id)
    ) {
      return false;
    }

    // If end is provided, validate it
    if (l.end !== null && l.end !== undefined) {
      if (
        typeof l.end !== 'number' ||
        !Number.isFinite(l.end) ||
        l.start >= l.end
      ) {
        return false;
      }
    }

    // Create a unique key for deduplication
    const endKey = l.end !== null && l.end !== undefined ? Math.round(l.end * 1000) : 'null';
    const key = `${Math.round(l.start * 1000)}-${endKey}`;
    if (bySpan.has(key)) return false; // duplicate span

    byId.add(l.id);
    bySpan.add(key);
    return true;
  });
};

/**
 * Persist a *sanitised* copy of the loops array for the given video ID.
 */
const persistLoopsForVideo = (videoId: string | null, loops: any[]) => {
  if (!videoId) return;
  try {
    localStorage.setItem(
      `loops-${videoId}`,
      JSON.stringify(sanitizeLoops(loops))
    );
  } catch {
    // storage might be unavailable; ignore
  }
};

/**
 * Load persisted loops for the given video ID, always sanitising on the way in.
 */
const loadLoopsForVideo = (videoId: string | null): any[] => {
  if (!videoId) return [];
  try {
    const data = localStorage.getItem(`loops-${videoId}`);
    const loops = data ? JSON.parse(data) : [];
    return sanitizeLoops(loops);
  } catch {
    return [];
  }
};

interface AudioState {
  // Audio source
  audioUrl: string | null;
  youtubeUrl: string | null;
  isYoutubeSource: boolean;

  // Playback state
  isPlaying: boolean;
  currentTime: number;
  duration: number;

  // Playback settings
  pitch: {
    octave: number; // -3 to 3
    semitone: number; // -12 to 12
    cents: number; // -100 to 100
  };
  volume: number; // 0 to 1

  // Loop settings
  loopActive: boolean;
  loopStart: number;
  loopEnd: number;
  loopDelay: number; // Delay before loop restarts

  // Markers
  markers: Array<{
    id: string;
    time: number;
    label: string;
  }>;

  // Beats
  beats: number[];

  // Lyrics
  lyrics: string;
  // Lyrics styling/settings
  lyricsFontSize: number;
  lyricsIsBold: boolean;
  lyricsIsItalic: boolean;
  lyricsIsUnderlined: boolean;

  // Saved loops
  savedLoops: Array<{
    id: string;
    name: string;
    start: number;
    end: number | null; // null means partial loop (only start set)
  }>;
  activeLoopId: string | null;
  showDisabledLoops: boolean;

  // Actions
  setAudioUrl: (url: string | null) => void;
  setYoutubeUrl: (url: string | null) => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  setPitch: (octave: number, semitone: number, cents: number) => void;
  setVolume: (volume: number) => void;
  setLoopActive: (active: boolean) => void;
  setLoopRegion: (start: number, end: number) => void;
  setLoopStart: (start: number) => void;
  setLoopEnd: (end: number) => void;
  setLoopDelay: (delay: number) => void;
  addMarker: (time: number, label: string) => void;
  removeMarker: (id: string) => void;
  updateMarker: (id: string, time: number, label: string) => void;
  addBeat: (time: number) => void;
  removeBeat: (time: number) => void;
  clearBeats: () => void;
  setLyrics: (lyrics: string) => void;
  setLyricsFontSize: (size: number) => void;
  toggleLyricsBold: () => void;
  toggleLyricsItalic: () => void;
  toggleLyricsUnderlined: () => void;
  addSavedLoop: (name: string, start: number, end: number | null) => void;
  renameSavedLoop: (id: string, name: string) => void;
  removeSavedLoop: (id: string) => void;
  updateSavedLoop: (id: string, start: number, end: number | null) => void;
  setActiveLoop: (id: string) => void;
  setShowDisabledLoops: (show: boolean) => void;
}

export const useAudioStore = create<AudioState>()(
  persist(
    (set) => ({
      // Initial state
      audioUrl: null,
      youtubeUrl: null,
      isYoutubeSource: false,
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      pitch: {
        octave: 0,
        semitone: 0,
        cents: 0,
      },
      volume: 1.0,
      loopActive: false,
      loopStart: 0,
      loopEnd: 0,
      loopDelay: 0,
      markers: [],
      beats: [],
      lyrics: '',
      // Initial lyrics settings
      lyricsFontSize: 16, // Default font size in px
      lyricsIsBold: false,
      lyricsIsItalic: false,
      lyricsIsUnderlined: false,
      savedLoops: [
        { id: 'test1', name: 'Loop 1', start: 30, end: 60 },
        { id: 'test2', name: 'Loop 2', start: 90, end: 120 },
        { id: 'test3', name: 'Partial Loop 1', start: 150, end: null }
      ],
      activeLoopId: null,
      showDisabledLoops: true,

      // Actions
      setAudioUrl: (url) => set({
        audioUrl: url,
        isYoutubeSource: false,
        youtubeUrl: null
      }),

      setYoutubeUrl: (url) => {
        // when switching videos, load persisted loops for that video
        const id = extractYouTubeId(url);
        const persisted = loadLoopsForVideo(id);
        set({
          youtubeUrl: url,
          isYoutubeSource: true,
          audioUrl: null,
          savedLoops: persisted,
          activeLoopId: persisted.length > 0 ? persisted[0].id : null,
          loopActive: persisted.length > 0,
          // Initialise the actual loop region from the first persisted loop
          loopStart: persisted.length > 0 ? persisted[0].start : 0,
          loopEnd:   persisted.length > 0 ? persisted[0].end   : 0,
        });
      },

      setIsPlaying: (isPlaying) => set({ isPlaying }),

      setCurrentTime: (newTime) => set((state) => {
        // Only update if time has changed significantly to prevent rapid updates / potential loops.
        // A threshold of 10ms (0.01s) should be fine enough for UI but coarse enough for stability.
        // Allow explicit seek to 0 even if current time is very close to 0.
        if (Math.abs(state.currentTime - newTime) > 0.01 || (newTime === 0 && state.currentTime !== 0)) {
          return { currentTime: newTime };
        }
        // No change, prevent re-render and potential loop
        return {};
      }),

      setDuration: (duration) => set({ duration }),

      setPitch: (octave, semitone, cents) => set({
        pitch: { octave, semitone, cents }
      }),

      setVolume: (volume) => set({ volume }),

      setLoopActive: (loopActive) => set(state => ({
        loopActive,
        // Clear active loop selection when deactivating loops
        activeLoopId: loopActive ? state.activeLoopId : null
      })),

      setLoopRegion: (loopStart, loopEnd) => set(() => {
        // Only activate looping if we have a valid end time that's different from start
        const shouldActivateLoop = loopEnd > loopStart + 0.1;
        return {
          loopStart,
          loopEnd,
          loopActive: shouldActivateLoop
        };
      }),

      setLoopStart: (loopStart) => set(state => ({
        loopStart,
        // Don't automatically activate looping when just setting start
        loopActive: state.loopEnd > loopStart + 0.1 ? state.loopActive : false
      })),

      setLoopEnd: (loopEnd) => set(state => ({
        loopEnd,
        // Activate looping if we now have a valid range
        loopActive: loopEnd > state.loopStart + 0.1
      })),

      setLoopDelay: (loopDelay) => set({ loopDelay }),

      addMarker: (time, label) => set((state) => ({
        markers: [
          ...state.markers,
          {
            id: crypto.randomUUID(),
            time,
            label
          }
        ]
      })),

      removeMarker: (id) => set((state) => ({
        markers: state.markers.filter(marker => marker.id !== id)
      })),

      updateMarker: (id, time, label) => set((state) => ({
        markers: state.markers.map(marker =>
          marker.id === id ? { ...marker, time, label } : marker
        )
      })),

      addBeat: (time) => set((state) => ({
        beats: [...state.beats, time].sort((a, b) => a - b)
      })),

      removeBeat: (time) => set((state) => ({
        beats: state.beats.filter(beat => Math.abs(beat - time) > 0.01)
      })),

      clearBeats: () => set({ beats: [] }),

      setLyrics: (lyrics) => set({ lyrics }),
      setLyricsFontSize: (size) => set({ lyricsFontSize: size }),
      toggleLyricsBold: () => set(state => ({ lyricsIsBold: !state.lyricsIsBold })),
      toggleLyricsItalic: () => set(state => ({ lyricsIsItalic: !state.lyricsIsItalic })),
      toggleLyricsUnderlined: () => set(state => ({ lyricsIsUnderlined: !state.lyricsIsUnderlined })),

      addSavedLoop: (name, start, end) => set(state => {
        const id = crypto.randomUUID();
        // Generate a unique friendly name if none supplied or duplicate.
        let loopName = name;
        if (!loopName || state.savedLoops.some(l => l.name === loopName)) {
          const highest = state.savedLoops.reduce((max, l) => {
            const m = /^Loop (\d+)$/.exec(l.name);
            return m ? Math.max(max, Number(m[1])) : max;
          }, 0);
          loopName = `Loop ${highest + 1}`;
        }

        // Check for an existing loop with same start/end (±10 ms)
        const dup = state.savedLoops.find(l => {
          const startMatch = Math.abs(l.start - start) < 0.01;
          if (end === null) {
            return startMatch && l.end === null;
          }
          return startMatch && l.end !== null && Math.abs(l.end - end) < 0.01;
        });
        const videoId = extractYouTubeId(state.youtubeUrl);

        if (dup) {
          // Re-persist just in case (no change) and re-select existing
          persistLoopsForVideo(videoId, state.savedLoops);
          return {
            savedLoops: state.savedLoops,
            activeLoopId: dup.id,
            loopStart: dup.start,
            loopEnd: dup.end || state.loopEnd, // Use current end if partial loop
            loopActive: true
          };
        }

        // Otherwise add a new one
        const newLoops = sanitizeLoops([
          ...state.savedLoops,
          { id, name: loopName, start, end }
        ]);
        persistLoopsForVideo(videoId, newLoops);
        return {
          savedLoops: newLoops,
          activeLoopId: id,
          loopStart: start,
          loopEnd: end || state.loopEnd, // Use current end if partial loop
          loopActive: true
        };
      }),

      renameSavedLoop: (id, name) => set(state => {
        const newLoops = sanitizeLoops(
          state.savedLoops.map(loop =>
            loop.id === id ? { ...loop, name } : loop
          )
        );
        // update persisted loops
        const videoId = extractYouTubeId(state.youtubeUrl);
        persistLoopsForVideo(videoId, newLoops);

        return { savedLoops: newLoops };
      }),

      removeSavedLoop: (id) => set(state => {
        const newLoops = sanitizeLoops(
          state.savedLoops.filter(loop => loop.id !== id)
        );
        // update persisted loops
        const videoId = extractYouTubeId(state.youtubeUrl);
        persistLoopsForVideo(videoId, newLoops);

        return {
          savedLoops: newLoops,
          activeLoopId: state.activeLoopId === id ? null : state.activeLoopId
        };
      }),

      updateSavedLoop: (id, start, end) => set(state => {
        const newLoops = sanitizeLoops(
          state.savedLoops.map(loop =>
            loop.id === id ? { ...loop, start, end } : loop
          )
        );
        // update persisted loops
        const videoId = extractYouTubeId(state.youtubeUrl);
        persistLoopsForVideo(videoId, newLoops);

        return { savedLoops: newLoops };
      }),

      setActiveLoop: (id) => {
        const loop = useAudioStore.getState().savedLoops.find(l => l.id === id);
        if (loop) {
          const state = useAudioStore.getState();
          set({
            activeLoopId: id,
            loopStart: loop.start,
            loopEnd: loop.end !== null ? loop.end : state.loopEnd, // Keep current end if partial loop
            loopActive: true
          });
        }
      },

      setShowDisabledLoops: (show) => set({ showDisabledLoops: show }),
    }),
    {
      name: 'audio-storage', // still using same key for lyrics; loops are handled per-video
      partialize: (state) => ({
        lyrics: state.lyrics,
        lyricsFontSize: state.lyricsFontSize,
        lyricsIsBold: state.lyricsIsBold,
        lyricsIsItalic: state.lyricsIsItalic,
        lyricsIsUnderlined: state.lyricsIsUnderlined,
      }),
    }
  )
);
