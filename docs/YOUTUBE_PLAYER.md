# YouTube Player Component

The YouTube Player component is responsible for loading and playing YouTube videos in the LearnSong application.

## Overview

The component uses the YouTube IFrame API to embed videos and provides controls for playback, tempo adjustment, and more. It integrates with the application's state management system (Zustand) to synchronize playback state across the application.

## Features

- Load YouTube videos by URL
- Play, pause, and stop playback
- Adjust playback speed (tempo)
- Adjust volume
- Display current time and duration

## Implementation Details

### YouTube IFrame API Integration

The component loads the YouTube IFrame API dynamically and creates a player instance when a video ID is available. It handles various player events (ready, state change, error) and updates the application state accordingly.



### Time Tracking

The component updates the current playback time at regular intervals (250ms) to keep the application state in sync with the actual video playback.

## Usage

```tsx
import YouTubePlayer from './components/YouTubePlayer';

// In your component
<YouTubePlayer url="https://www.youtube.com/watch?v=dQw4w9WgXcQ" />
```

## Limitations

- YouTube's API only supports specific playback rates, limiting the granularity of tempo control
- Pitch preservation is handled by YouTube's player and may not be as high quality as dedicated audio processing libraries
- YouTube's terms of service may restrict certain features or usage patterns. Note that playback rate outside of the standard options (0.25x to 2x) is technically non-compliant, although widely used.

## Future Enhancements

- Integration with Web Audio API for better audio processing
- Support for custom playback rates using audio extraction and processing
- Improved error handling and user feedback
- Support for YouTube playlists and other video sources
