# State Management

LearnSong uses Zustand for state management. This document describes the structure and usage of the application's state.

## Overview

The application state is managed through a single Zustand store called `useAudioStore`. This store contains all the state related to audio playback, including the audio source, playback state, playback settings, loop settings, markers, and beats.

## Store Structure

The `useAudioStore` store has the following structure:

### Audio Source

- `audioUrl`: URL of the audio file (null if using YouTube)
- `youtubeUrl`: URL of the YouTube video (null if using audio file)
- `isYoutubeSource`: Boolean indicating whether the current source is a YouTube video

### Playback State

- `isPlaying`: Boolean indicating whether audio is currently playing
- `currentTime`: Current playback position in seconds
- `duration`: Total duration of the audio in seconds

### Playback Settings

- `pitch`: Object containing pitch adjustment settings
  - `octave`: Octave adjustment (-3 to 3)
  - `semitone`: Semitone adjustment (-12 to 12)
  - `cents`: Fine pitch adjustment (-100 to 100)
- `volume`: Playback volume (0 to 1)

### Loop Settings

- `loopActive`: Boolean indicating whether loop is active
- `loopStart`: Loop start position in seconds
- `loopEnd`: Loop end position in seconds
- `loopDelay`: Delay before loop restarts in seconds

### Markers and Beats

- `markers`: Array of marker objects
  - `id`: Unique identifier
  - `time`: Position in seconds
  - `label`: Text label
- `beats`: Array of beat positions in seconds
- `lyrics`: String containing the lyrics text
  - `lyricsFontSize`: Font size of the lyrics text in pixels
  - `lyricsIsBold`: Boolean indicating if the lyrics text is bold
  - `lyricsIsItalic`: Boolean indicating if the lyrics text is italic
  - `lyricsIsUnderlined`: Boolean indicating if the lyrics text is underlined

## Actions

The store provides the following actions to modify the state:

- `setAudioUrl`: Set the audio file URL
- `setYoutubeUrl`: Set the YouTube video URL
- `setIsPlaying`: Set the playing state
- `setCurrentTime`: Set the current playback position
- `setDuration`: Set the total duration
- `setTempo`: Set the playback speed
- `setPitch`: Set the pitch adjustment
- `setVolume`: Set the playback volume
- `setLoopActive`: Enable or disable looping
- `setLoopRegion`: Set the loop start and end positions
- `setLoopDelay`: Set the delay before loop restarts
- `addMarker`: Add a marker at the specified position
- `removeMarker`: Remove a marker by ID
- `updateMarker`: Update a marker's position and label
- `addBeat`: Add a beat at the specified position
- `removeBeat`: Remove a beat at the specified position
- `clearBeats`: Remove all beats
- `setLyrics`: Set the lyrics text
- `setLyricsFontSize`: Set the font size for lyrics
- `toggleLyricsBold`: Toggle bold style for lyrics
- `toggleLyricsItalic`: Toggle italic style for lyrics
- `toggleLyricsUnderlined`: Toggle underlined style for lyrics

## Usage

```tsx
import { useAudioStore } from '../store/useAudioStore';

// In a component
const MyComponent = () => {
  const {
    isPlaying,
    setIsPlaying,
    currentTime,
    duration,
    tempo,
    setTempo
  } = useAudioStore();

  // Use the state and actions
  return (
    <div>
      <button onClick={() => setIsPlaying(!isPlaying)}>
        {isPlaying ? 'Pause' : 'Play'}
      </button>
      <input
        type="range"
        min="0.25"
        max="2"
        step="0.05"
        value={tempo}
        onChange={(e) => setTempo(parseFloat(e.target.value))}
      />
    </div>
  );
};
```

## Future Enhancements

- Persistence to localStorage
- JSON export/import for project sharing
- Undo/redo functionality
- Support for multiple audio tracks
