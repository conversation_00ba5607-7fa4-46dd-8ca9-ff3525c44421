# Draggable Lyrics Popup

The Draggable Lyrics Popup is a floating window that allows users to enter and edit lyrics while watching YouTube videos. The window can be dragged around the screen and positioned wherever is most convenient for the user.

## Features

- **Draggable Window**: The popup can be freely dragged around the screen.
- **Persistent Storage**: Lyrics are stored in the application state and persisted between sessions using `localStorage`.
- **Overlay Protection**: When dragging, an invisible overlay is created to prevent the YouTube player from capturing mouse events, ensuring smooth dragging even when moving over the video.
- **Font Size Control**: Buttons are provided to increase or decrease the font size of the lyrics text. The size is also persisted.
- **Text Style Control**: Buttons are provided to toggle bold, italic, and underlined styles for the lyrics text. These styles are applied to the entire text area and are persisted.
- **Keyboard Shortcuts for Style**: Keyboard shortcuts (Cmd/Ctrl + B, I, U) are available to quickly toggle bold, italic, and underlined styles when the popup is visible.
- **Resizable**: The popup can be resized via the bottom and bottom-right edges.

## Implementation Details

### Dragging Functionality

The dragging functionality is implemented using the `react-draggable` library. The component defines a draggable handle (the popup header) that users can grab to move the window. The rest of the popup content is not draggable to allow for normal text selection and editing.

### Overlay System

To ensure smooth dragging even when the cursor moves over the YouTube player (which would normally capture mouse events), an overlay is dynamically created when dragging begins. This overlay covers the entire viewport and captures all mouse events until dragging ends, at which point it is removed.

### State Management

The lyrics text and its styling settings (font size, bold, italic, underlined) are stored in the global application state using Zustand. This ensures that the lyrics and their presentation persist between sessions and can be accessed by other components if needed. The state is automatically saved to and loaded from `localStorage`.

### Font Size and Style Controls

Buttons are added within the popup header area to control the font size ("A-", "A+") and text style ("B", "I", "U"). Clicking these buttons updates the corresponding state properties in the Zustand store, which triggers a re-render of the textarea with the new styles applied.

### Keyboard Shortcuts

Global keyboard listeners are implemented to capture Cmd/Ctrl + B, I, and U key combinations. When the lyrics popup is visible, these shortcuts will toggle the bold, italic, and underlined styles respectively by calling the corresponding actions in the Zustand store. `preventDefault` is used to prevent default browser actions for these shortcuts.

### Resizing

Resize handles are implemented along the bottom edge and in the bottom-right corner, allowing the user to freely adjust the dimensions of the popup window. An overlay similar to the dragging overlay is used during resizing to capture mouse events outside the popup iframe.

## Usage

The lyrics popup can be toggled using the "Show Lyrics" button at the bottom right of the application. When visible, users can:

1. Drag the popup by its header to reposition it
2. Enter or edit lyrics in the text area.
3. Adjust font size using the "A-" and "A+" buttons.
4. Toggle bold, italic, or underlined styles using the "B", "I", and "U" buttons or Cmd/Ctrl + B, I, U keyboard shortcuts.
5. Resize the popup by dragging the bottom edge or corner.
6. Close the popup using the × button in the header.

The lyrics content is automatically saved as the user types.
