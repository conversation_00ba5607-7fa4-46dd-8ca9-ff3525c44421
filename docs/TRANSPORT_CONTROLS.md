# Transport Controls

The Transport Controls component provides user interface elements for controlling audio playback in the LearnSong application.

## Overview

The `TransportControls` component displays playback controls (play, pause, stop) and current time and duration. It interacts with the application's state management system (Zustand) to control playback.

## Features

- Play/pause button
- Stop button
- Current time and duration display
- Audio timeline with draggable loop handles

## Implementation Details

### Playback Control

The component provides buttons for play/pause and stop. When clicked, these buttons update the application state, which in turn affects the actual audio playback through the YouTube player or audio engine.

### Time Display

The component displays the current playback time and total duration in a human-readable format (minutes:seconds). This information is updated in real-time as the audio plays.



### Audio Timeline

The component includes a visual timeline using the `YouTubeTimeline` component. This allows users to see the current playback position and create loop regions by clicking and dragging handles. The timeline is synchronized with the audio playback and updates in real-time via custom events. Users can click on the timeline to seek to a specific position and drag the handles to adjust the loop start and end points. Interaction with the timeline is disabled while loop handles or the playhead are being dragged.

## Usage

```tsx
import TransportControls from './components/TransportControls';

// In your component
<TransportControls />
```

## Future Enhancements

- Additional controls for pitch adjustment
- Marker controls (add/remove markers)
- Beat controls (add/remove beats)
- Keyboard shortcut indicators
- Customizable keyboard shortcuts
- Enhanced waveform visualization with zoom controls
