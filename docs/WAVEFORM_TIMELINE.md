# YouTube Timeline Component

The YouTube Timeline component provides a visual timeline with draggable handles for creating and adjusting loops in the LearnSong application. It's fully integrated with the YouTube player controls.

## Overview

The `YouTubeTimeline` component (in the file `WaveformTimeline.tsx`) displays a timeline that allows users to visualize and control the YouTube video playback position and create loops by dragging handles to define the start and end points. The timeline is fully synchronized with the YouTube player.

## Features

- Visual timeline with time markers and time labels
- Click to seek to a specific position in the YouTube video
- Shift+click to set loop end points
- Ctrl/Cmd+click to toggle loop on/off
- Draggable handles for adjusting loop start and end points
- Prominent playhead with time tooltip for tracking current position
- Zoom in/out functionality for precise control
- Automatic looping when playback reaches the end point
- Display of loop region and time information
- Full integration with YouTube player controls

## Implementation Details

### Timeline Visualization

The component displays a timeline with markers at regular intervals and time labels. The current playback position is indicated by a prominent playhead (vertical line with a circular handle and time tooltip) that moves as the YouTube video plays.

### YouTube Integration

The component directly interacts with the YouTube player through the YouTube IFrame API. It can seek the video to specific positions, and it stays synchronized with the YouTube player's current time. When you click on the timeline or drag the handles, the YouTube video position updates accordingly.

### Draggable Handles

The component includes draggable handles for adjusting the loop start and end points. Users can click and drag these handles to precisely define the loop region. The handles show the current time position while being dragged, and the YouTube video position updates in real-time as you drag.

### Zoom Functionality

The component includes zoom in/out buttons that allow users to adjust the level of detail shown in the timeline. This is particularly useful for precise loop selection in longer videos. The timeline becomes scrollable when zoomed in, with auto-scrolling to keep the playhead visible.

### Keyboard Shortcuts

The component supports several keyboard shortcuts for easier interaction:
- Shift+click to set loop points or create new loops by dragging
- Ctrl/Cmd+click to toggle loop on/off

### Loop Creation

When no loop is active, shift+clicking on the timeline will:
1. Set the loop start at the clicked position
2. Allow immediate dragging to set both start and end bounds in one gesture
3. Support bidirectional dragging (left or right from the initial click point)
4. Automatically determine which position becomes the start vs end based on drag direction

### Synchronization

The component synchronizes with the application state in several ways:
- It updates the timeline position when the current playback time changes
- It updates the loop region when the loop settings change
- It updates the application state when the user drags the loop handles

## Usage

```tsx
import YouTubeTimeline from './components/WaveformTimeline';

// In your component
<YouTubeTimeline />
```

The component automatically detects and interacts with the YouTube player on the page. It doesn't require any additional props as it uses the global Zustand store to access and update the playback state.

## Future Enhancements

- Multiple loop regions for more complex practice sessions
- Markers for important points in the video (e.g., verse, chorus)
- Additional keyboard shortcuts for more advanced control
- Customizable colors and appearance
- Waveform visualization if reliable audio data becomes available
- Touch/mobile support for dragging handles
- Ability to save and load loop points
- Integration with YouTube chapters if available
- Thumbnail previews when hovering over the timeline
