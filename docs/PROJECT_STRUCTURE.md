# LearnSong Project Structure

This document outlines the structure and components of the LearnSong application.

## Overview

LearnSong is a web application that allows musicians to load audio files or YouTube URLs, slow them down without altering pitch, loop tricky bars, drop visual markers and beats, and save progress locally. The application is built using React, TypeScript, and various web audio APIs.

## Project Structure

```
learnsong/
├── docs/                  # Documentation
├── public/                # Static assets
├── src/
│   ├── assets/            # Images, icons, etc.
│   ├── components/        # React components
│   │   ├── YouTubePlayer.tsx      # YouTube video player component
│   │   ├── DraggableLyricsPopup.tsx # Draggable popup for editing lyrics
│   │   ├── YouTubeUrlInput.tsx    # Form for entering YouTube URLs
│   │   ├── TransportControls.tsx  # Playback controls (play, pause, tempo)
│   │   ├── WaveformTimeline.tsx   # Audio timeline with draggable loop handles
│   │   └── LoopControls.tsx       # Loop start/end controls
│   ├── store/             # State management
│   │   └── useAudioStore.ts       # Zustand store for audio state
│   ├── App.tsx            # Main application component
│   ├── main.tsx           # Application entry point
│   └── index.css          # Global styles with Tailwind
├── tailwind.config.js     # Tailwind CSS configuration
├── postcss.config.js      # PostCSS configuration
├── tsconfig.json          # TypeScript configuration
└── vite.config.ts         # Vite configuration
```

## Key Components

### YouTubePlayer

The `YouTubePlayer` component is responsible for loading and playing YouTube videos. It uses the YouTube IFrame API to embed videos and provides controls for playback, tempo adjustment, and more.

### YouTubeUrlInput

The `YouTubeUrlInput` component provides a form for users to enter YouTube URLs. When a URL is submitted, it is stored in the application state and used to load the video.

### TransportControls

The `TransportControls` component provides UI controls for playback (play, pause, stop) and tempo adjustment. It displays the current playback time and duration and includes a waveform visualization with region selection for looping.

### AudioTimeline

The `AudioTimeline` component (in the file `WaveformTimeline.tsx`) displays a visual timeline with draggable handles for adjusting loop start and end points. It allows users to click to seek and drag handles to precisely define loop regions.

### LoopControls

The `LoopControls` component provides UI controls for setting loop start and end points, as well as enabling/disabling looping and setting a delay between loops.

### DraggableLyricsPopup
A floating, draggable popup window where users can type and edit lyrics. It integrates with the state store to persist lyrics and styling settings.

## State Management

The application uses Zustand for state management. The `useAudioStore` store contains all the state related to audio playback, including:

- Audio source (file or YouTube URL)
- Playback state (playing, current time, duration)
- Playback settings (tempo, pitch, volume)
- Loop settings (start, end, delay)
- Markers and beats

## Future Enhancements

The current implementation includes playing YouTube videos with tempo control, waveform visualization, and loop region selection. Future enhancements will include:

1. Markers and beats
2. Local file upload
3. Persistence using localStorage
4. JSON export/import for project sharing
5. Enhanced waveform visualization with zoom controls
6. Multiple loop regions
