/**
 * Test for session ID matching logic in recording overwrite functionality
 */

// Simulate the session ID matching logic from the component
function testSessionIdMatching(notes, currentSessionId) {
  const protectedNotes = [];
  const overwritableNotes = [];
  
  notes.forEach(note => {
    const isRecordingNote = note.id.toString().startsWith('rec_');
    const isCurrentSessionNote = isRecordingNote && currentSessionId && note.id.toString().startsWith(`rec_${currentSessionId}_`);
    
    if (isCurrentSessionNote) {
      protectedNotes.push(note.id);
    } else {
      overwritableNotes.push(note.id);
    }
  });
  
  return {
    overwritableCount: overwritableNotes.length,
    protectedNotes,
    overwritableNotes
  };
}

const testScenarios = [
  {
    name: "Initial state - no recording session",
    currentSessionId: null,
    notes: [
      { id: 'n1', pitch: 60, startTime: 0, duration: 1, velocity: 80 }, // Pre-loaded note
      { id: 'n2', pitch: 64, startTime: 1, duration: 1, velocity: 90 }, // Pre-loaded note
    ],
    expectedOverwritableCount: 2,
    expectedProtectedNotes: [],
    expectedOverwritableNotes: ['n1', 'n2']
  },
  {
    name: "Second recording session - should overwrite first session notes",
    currentSessionId: "session_9876543210_def456",
    notes: [
      { id: 'n1', pitch: 60, startTime: 0, duration: 1, velocity: 80 }, // Pre-loaded note
      { id: 'rec_session_1234567890_abc123_60_xyz789', pitch: 60, startTime: 2, duration: 0.5, velocity: 85 }, // Previous session note
      { id: 'rec_session_1234567890_abc123_64_def456', pitch: 64, startTime: 3, duration: 0.5, velocity: 90 }, // Previous session note
      { id: 'rec_session_9876543210_def456_67_ghi123', pitch: 67, startTime: 4, duration: 0.5, velocity: 95 }, // Current session note
    ],
    expectedOverwritableCount: 3,
    expectedProtectedNotes: ['rec_session_9876543210_def456_67_ghi123'],
    expectedOverwritableNotes: ['n1', 'rec_session_1234567890_abc123_60_xyz789', 'rec_session_1234567890_abc123_64_def456']
  },
  {
    name: "Edge case - similar session IDs",
    currentSessionId: "session_1234567890_abc123",
    notes: [
      { id: 'rec_session_1234567890_abc123_60_xyz789', pitch: 60, startTime: 2, duration: 0.5, velocity: 85 }, // Current session
      { id: 'rec_session_1234567890_abc123456_64_def456', pitch: 64, startTime: 3, duration: 0.5, velocity: 90 }, // Different session (longer ID)
      { id: 'rec_session_1234567890_abc12_67_ghi123', pitch: 67, startTime: 4, duration: 0.5, velocity: 95 }, // Different session (shorter ID)
    ],
    expectedOverwritableCount: 2,
    expectedProtectedNotes: ['rec_session_1234567890_abc123_60_xyz789'],
    expectedOverwritableNotes: ['rec_session_1234567890_abc123456_64_def456', 'rec_session_1234567890_abc12_67_ghi123']
  }
];

function runSessionIdTests() {
  console.log('=== Session ID Matching Tests ===\n');
  
  let passedTests = 0;
  let totalTests = testScenarios.length;
  
  testScenarios.forEach((scenario, index) => {
    console.log(`Test ${index + 1}: ${scenario.name}`);
    console.log(`  Current session ID: ${scenario.currentSessionId || 'null'}`);
    console.log(`  Notes:`, scenario.notes.map(n => n.id));
    
    const result = testSessionIdMatching(scenario.notes, scenario.currentSessionId);
    
    const overwritableCountMatch = result.overwritableCount === scenario.expectedOverwritableCount;
    const protectedNotesMatch = JSON.stringify(result.protectedNotes.sort()) === JSON.stringify(scenario.expectedProtectedNotes.sort());
    const overwritableNotesMatch = JSON.stringify(result.overwritableNotes.sort()) === JSON.stringify(scenario.expectedOverwritableNotes.sort());
    
    const passed = overwritableCountMatch && protectedNotesMatch && overwritableNotesMatch;
    
    console.log(`  Expected overwritable count: ${scenario.expectedOverwritableCount}`);
    console.log(`  Actual overwritable count: ${result.overwritableCount} ${overwritableCountMatch ? '✅' : '❌'}`);
    console.log(`  Expected protected notes: [${scenario.expectedProtectedNotes.join(', ')}]`);
    console.log(`  Actual protected notes: [${result.protectedNotes.join(', ')}] ${protectedNotesMatch ? '✅' : '❌'}`);
    console.log(`  Expected overwritable notes: [${scenario.expectedOverwritableNotes.join(', ')}]`);
    console.log(`  Actual overwritable notes: [${result.overwritableNotes.join(', ')}] ${overwritableNotesMatch ? '✅' : '❌'}`);
    console.log(`  Status: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log('');
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`=== Test Results ===`);
  console.log(`Passed: ${passedTests}/${totalTests}`);
  console.log(`Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Session ID matching logic is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the session ID matching logic.');
  }
}

// Run the tests
runSessionIdTests();
