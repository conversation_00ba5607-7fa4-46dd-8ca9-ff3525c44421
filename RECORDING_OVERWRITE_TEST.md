# MIDI Recording Overwrite Test Instructions

## Issue Fixed
The recording feature now properly overwrites notes during the entire duration of recording, including:
- **Continuous overwrite**: Existing notes are removed as the playback position moves through them during recording, even when no keys are pressed
- **Key-press overwrite**: Notes are also overwritten when you press keys at positions with existing notes
- **Loop/seek detection**: Automatically detects when playback moves backward and resets overwrite tracking
- Works consistently on ALL subsequent passes whether you come back to a region by:
  - Clicking on the timeline to seek
  - Natural loop restart
  - Forcefully restarting with Space key
  - Any other navigation method

## Test Scenario

### Setup
1. Open the application in your browser (http://127.0.0.1:5173/)
2. Navigate to the MIDI editor
3. Set a reference time by pressing `R` 
4. Enable recording mode by pressing `Shift+R`

### Test Steps

#### Step 1: Initial Recording
1. Press and hold key `1` (first scale degree) for about 1 second
2. Release the key
3. You should see a note appear in the MIDI editor
4. Check the browser console - you should see debug messages about recording

#### Step 2: Move Playback Position
1. Click somewhere else on the timeline to move the playback position
2. Or wait for the loop to restart naturally
3. Or press Space to restart playback

#### Step 3: Test Continuous Overwrite (All Pitches)
1. Record multiple notes at different pitches (e.g., press keys `1`, `2`, `3` at different times)
2. Navigate back to the beginning of the recorded region
3. **Do NOT press any keys** - just let the playback move through the existing notes while recording is active
4. **ALL existing notes should be automatically removed** as the playback position passes through them, regardless of pitch

#### Expected Results for Continuous Overwrite
- **All original notes should be removed automatically** as playback moves through them (not just the same pitch)
- No new notes should be created (since no keys are pressed)
- The console should show debug messages like:
  ```
  🔄 Continuous overwrite: removing note [noteId] (C4) at beat X.XX-X.XX
  🔄 Continuous overwrite: removing note [noteId] (D4) at beat X.XX-X.XX
  🔄 Continuous overwrite: removed 2 notes in range X.XX-X.XX
  ```

#### Step 4: Test Recording New Notes
1. Navigate back to the same time position where you had existing notes
2. Press and hold key `1` at the position
3. Release the key
4. Press and hold key `2` at a different position
5. Release the key

#### Expected Results for Recording New Notes
- New notes should be created at the pressed positions
- The continuous overwrite should have already cleared any conflicting existing notes
- The console should show debug messages like:
  ```
  🔍 Adding new recording note at pitch 60 (C4) at beat X.XX
  ➕ Adding new note: [newNoteId] (C4) at beat X.XX
  ```

#### Step 5: Test Multiple Pitches at Same Time
1. Navigate to a clean section (no existing notes)
2. Press and hold multiple keys simultaneously (e.g., `1` and `2`) at the same time position
3. Release the keys
4. Both notes should be created and coexist at the same time position

#### Step 6: Test Recording Note Overwrite (Critical Test)
1. Record some notes in a section (e.g., press keys `1`, `2`, `3` at different times)
2. **Stop recording** (Shift+R to turn off recording mode)
3. **Start recording again** (Shift+R to turn on recording mode)
4. **Navigate back** to the recorded section (click on timeline to seek back)
5. **Do NOT press any keys** - just let playback move through the previously recorded notes while recording is active
6. **ALL previously recorded notes should be removed** on this pass

#### Expected Results for Recording Note Overwrite
- Console should show: `🔴 Recording started at beat X.XX - Session ID: session_XXXXX`
- Console should show: `🔄 Playback moved backward: from beat X.XX to Y.YY - resetting overwrite tracking`
- Console should show: `🔄 Continuous overwrite: removing recorded note rec_XXXXX`
- All previously recorded notes should be removed as playback progresses through them
- Only notes from the current recording session should be protected

#### Step 7: Test Duration Recording
1. Press and hold key `1` for 2 seconds
2. Release the key
3. The note should have the correct duration (about 2 seconds worth of beats)
4. Console should show: `✅ Updated note duration: [noteId] now has duration X.XX beats`

## Debug Information

The console will show detailed information about:
- **Recording state**: When recording starts/stops and at what beat position
- **Backward movement detection**: When playback moves backward (loop/seek) and tracking resets
- **Continuous overwrite**: Which notes are automatically removed as playback progresses during recording
- **Range checking**: What beat ranges are being checked for overwrite
- **Key-press overwrite**: Which notes are being checked for overwriting when keys are pressed
- Which notes are actually overwritten in both scenarios
- When new notes are added
- When note durations are updated

### Key Debug Messages to Look For:
```
🔴 Recording started at beat X.XX (video time: Y.YYs) - Session ID: session_XXXXX
🔄 Playback moved backward: from beat X.XX to Y.YY - resetting overwrite tracking (Session: session_XXXXX)
🔄 Continuous overwrite check: beat X.XX, range Y.YY-Z.ZZ, lastOverwrite: A.AA
🔄 Checking N overwritable notes for removal in range X.XX-Y.YY (Session: session_XXXXX)
🔄 Continuous overwrite: removing recorded note rec_session_XXXXX (C4) at beat X.XX-Y.YY
🔄 Continuous overwrite: removing existing note n1 (C4) at beat X.XX-Y.YY
```

## Troubleshooting

If overwriting doesn't work:
1. Check the console for error messages
2. Verify that recording mode is enabled (Shift+R)
3. Ensure you have set a reference time (R key)
4. Make sure you're recording at the same pitch and approximately the same time position

## Technical Details

The overwrite logic now works in two phases:

### Recording Session Management
- Each recording session gets a unique session ID (e.g., `session_1234567890_abc123`)
- Notes created during recording include the session ID in their note ID (e.g., `rec_session_1234567890_abc123_60_xyz789`)
- Session ID is generated when recording starts and cleared when recording stops

### Continuous Overwrite (Primary Mechanism)
- **Overwrites notes across ALL pitches** as playback progresses during recording
- **Overwrites ALL note types**: existing notes, notes from previous recording sessions, manually added notes
- **Only protects notes from the CURRENT recording session** (matching current session ID)
- Uses a tolerance of 0.05 beats for overlap detection
- Automatically removes existing notes as the playback position moves through them
- Works regardless of whether any keys are currently pressed

### Key-Press Recording (Secondary)
- Simply adds new notes when keys are pressed
- Notes are tagged with the current recording session ID
- No longer needs to check for conflicts since continuous overwrite handles that
- Preserves the full duration recording functionality
